// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.1
// source: agent/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server   *Server   `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	App      *App      `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
	Engine   *Engine   `protobuf:"bytes,3,opt,name=engine,proto3" json:"engine,omitempty"`
	Schedule *Schedule `protobuf:"bytes,4,opt,name=schedule,proto3" json:"schedule,omitempty"`
	Model    *Model    `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`
	Oss      *OSS      `protobuf:"bytes,6,opt,name=oss,proto3" json:"oss,omitempty"`
	Datahub  *Datahub  `protobuf:"bytes,7,opt,name=datahub,proto3" json:"datahub,omitempty"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetApp() *App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Bootstrap) GetEngine() *Engine {
	if x != nil {
		return x.Engine
	}
	return nil
}

func (x *Bootstrap) GetSchedule() *Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *Bootstrap) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *Bootstrap) GetOss() *OSS {
	if x != nil {
		return x.Oss
	}
	return nil
}

func (x *Bootstrap) GetDatahub() *Datahub {
	if x != nil {
		return x.Datahub
	}
	return nil
}

type Datahub struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable        bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	Port          int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	DatahubConfig string `protobuf:"bytes,3,opt,name=datahubConfig,proto3" json:"datahubConfig,omitempty"`
}

func (x *Datahub) Reset() {
	*x = Datahub{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Datahub) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Datahub) ProtoMessage() {}

func (x *Datahub) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Datahub.ProtoReflect.Descriptor instead.
func (*Datahub) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Datahub) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Datahub) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Datahub) GetDatahubConfig() string {
	if x != nil {
		return x.DatahubConfig
	}
	return ""
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MainPort  int32 `protobuf:"varint,1,opt,name=mainPort,proto3" json:"mainPort,omitempty"`
	InnerPort int32 `protobuf:"varint,2,opt,name=InnerPort,proto3" json:"InnerPort,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Server) GetMainPort() int32 {
	if x != nil {
		return x.MainPort
	}
	return 0
}

func (x *Server) GetInnerPort() int32 {
	if x != nil {
		return x.InnerPort
	}
	return 0
}

type Engine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url               string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Username          string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password          string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	OptionFeatures    string `protobuf:"bytes,4,opt,name=optionFeatures,proto3" json:"optionFeatures,omitempty"`
	QuotaSet          string `protobuf:"bytes,5,opt,name=quotaSet,proto3" json:"quotaSet,omitempty"`
	Labels            string `protobuf:"bytes,6,opt,name=labels,proto3" json:"labels,omitempty"`
	ResourceType      string `protobuf:"bytes,7,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	MountPath         string `protobuf:"bytes,8,opt,name=mountPath,proto3" json:"mountPath,omitempty"`
	EnableShellCheck  bool   `protobuf:"varint,9,opt,name=enableShellCheck,proto3" json:"enableShellCheck,omitempty"`
	MinWaitShellTime  int64  `protobuf:"varint,10,opt,name=minWaitShellTime,proto3" json:"minWaitShellTime,omitempty"`
	EngineMetricsUrl  string `protobuf:"bytes,11,opt,name=engineMetricsUrl,proto3" json:"engineMetricsUrl,omitempty"`
	EngineMetricsType string `protobuf:"bytes,12,opt,name=engineMetricsType,proto3" json:"engineMetricsType,omitempty"`
}

func (x *Engine) Reset() {
	*x = Engine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Engine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Engine) ProtoMessage() {}

func (x *Engine) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Engine.ProtoReflect.Descriptor instead.
func (*Engine) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Engine) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Engine) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Engine) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Engine) GetOptionFeatures() string {
	if x != nil {
		return x.OptionFeatures
	}
	return ""
}

func (x *Engine) GetQuotaSet() string {
	if x != nil {
		return x.QuotaSet
	}
	return ""
}

func (x *Engine) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *Engine) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Engine) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *Engine) GetEnableShellCheck() bool {
	if x != nil {
		return x.EnableShellCheck
	}
	return false
}

func (x *Engine) GetMinWaitShellTime() int64 {
	if x != nil {
		return x.MinWaitShellTime
	}
	return 0
}

func (x *Engine) GetEngineMetricsUrl() string {
	if x != nil {
		return x.EngineMetricsUrl
	}
	return ""
}

func (x *Engine) GetEngineMetricsType() string {
	if x != nil {
		return x.EngineMetricsType
	}
	return ""
}

type Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host             string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	NacosSwitchIsOn  bool   `protobuf:"varint,2,opt,name=nacosSwitchIsOn,proto3" json:"nacosSwitchIsOn,omitempty"`
	NacosAddr        string `protobuf:"bytes,3,opt,name=nacosAddr,proto3" json:"nacosAddr,omitempty"`
	NacosPort        int32  `protobuf:"varint,4,opt,name=nacosPort,proto3" json:"nacosPort,omitempty"`
	NacosNamespaceId string `protobuf:"bytes,5,opt,name=nacosNamespaceId,proto3" json:"nacosNamespaceId,omitempty"`
	NotifyUri        string `protobuf:"bytes,6,opt,name=notifyUri,proto3" json:"notifyUri,omitempty"`
}

func (x *Schedule) Reset() {
	*x = Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schedule) ProtoMessage() {}

func (x *Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schedule.ProtoReflect.Descriptor instead.
func (*Schedule) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Schedule) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Schedule) GetNacosSwitchIsOn() bool {
	if x != nil {
		return x.NacosSwitchIsOn
	}
	return false
}

func (x *Schedule) GetNacosAddr() string {
	if x != nil {
		return x.NacosAddr
	}
	return ""
}

func (x *Schedule) GetNacosPort() int32 {
	if x != nil {
		return x.NacosPort
	}
	return 0
}

func (x *Schedule) GetNacosNamespaceId() string {
	if x != nil {
		return x.NacosNamespaceId
	}
	return ""
}

func (x *Schedule) GetNotifyUri() string {
	if x != nil {
		return x.NotifyUri
	}
	return ""
}

type App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region                  string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Product                 string `protobuf:"bytes,2,opt,name=product,proto3" json:"product,omitempty"`
	PodId                   string `protobuf:"bytes,3,opt,name=podId,proto3" json:"podId,omitempty"`
	NodeId                  string `protobuf:"bytes,4,opt,name=nodeId,proto3" json:"nodeId,omitempty"`
	PodIp                   string `protobuf:"bytes,5,opt,name=podIp,proto3" json:"podIp,omitempty"`
	NodeIp                  string `protobuf:"bytes,6,opt,name=nodeIp,proto3" json:"nodeIp,omitempty"`
	IsAuth                  bool   `protobuf:"varint,7,opt,name=isAuth,proto3" json:"isAuth,omitempty"`
	CompleteThreadNum       int32  `protobuf:"varint,8,opt,name=completeThreadNum,proto3" json:"completeThreadNum,omitempty"`
	StartDelayTime          int32  `protobuf:"varint,9,opt,name=startDelayTime,proto3" json:"startDelayTime,omitempty"`
	ExitWaitTask            bool   `protobuf:"varint,10,opt,name=exitWaitTask,proto3" json:"exitWaitTask,omitempty"`
	ReStartLostTask         bool   `protobuf:"varint,11,opt,name=reStartLostTask,proto3" json:"reStartLostTask,omitempty"`
	MaxStartRetry           int32  `protobuf:"varint,12,opt,name=maxStartRetry,proto3" json:"maxStartRetry,omitempty"`
	ExitWaitSecond          int32  `protobuf:"varint,13,opt,name=exitWaitSecond,proto3" json:"exitWaitSecond,omitempty"`
	NetworkCard             string `protobuf:"bytes,14,opt,name=networkCard,proto3" json:"networkCard,omitempty"`
	IsQuotaAlarm            bool   `protobuf:"varint,15,opt,name=isQuotaAlarm,proto3" json:"isQuotaAlarm,omitempty"`
	UuidFromIp              bool   `protobuf:"varint,16,opt,name=UuidFromIp,proto3" json:"UuidFromIp,omitempty"`
	CollectProcessCmdPrefix string `protobuf:"bytes,17,opt,name=collectProcessCmdPrefix,proto3" json:"collectProcessCmdPrefix,omitempty"`
	CollectDiskParentDir    string `protobuf:"bytes,18,opt,name=collectDiskParentDir,proto3" json:"collectDiskParentDir,omitempty"`
	CollectFrequency        int32  `protobuf:"varint,19,opt,name=collectFrequency,proto3" json:"collectFrequency,omitempty"`
	Tag                     string `protobuf:"bytes,20,opt,name=tag,proto3" json:"tag,omitempty"`
	GpuThresholdEnable      bool   `protobuf:"varint,21,opt,name=gpuThresholdEnable,proto3" json:"gpuThresholdEnable,omitempty"`
	GpuThreshold            string `protobuf:"bytes,22,opt,name=gpuThreshold,proto3" json:"gpuThreshold,omitempty"`
}

func (x *App) Reset() {
	*x = App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*App) ProtoMessage() {}

func (x *App) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use App.ProtoReflect.Descriptor instead.
func (*App) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *App) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *App) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *App) GetPodId() string {
	if x != nil {
		return x.PodId
	}
	return ""
}

func (x *App) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *App) GetPodIp() string {
	if x != nil {
		return x.PodIp
	}
	return ""
}

func (x *App) GetNodeIp() string {
	if x != nil {
		return x.NodeIp
	}
	return ""
}

func (x *App) GetIsAuth() bool {
	if x != nil {
		return x.IsAuth
	}
	return false
}

func (x *App) GetCompleteThreadNum() int32 {
	if x != nil {
		return x.CompleteThreadNum
	}
	return 0
}

func (x *App) GetStartDelayTime() int32 {
	if x != nil {
		return x.StartDelayTime
	}
	return 0
}

func (x *App) GetExitWaitTask() bool {
	if x != nil {
		return x.ExitWaitTask
	}
	return false
}

func (x *App) GetReStartLostTask() bool {
	if x != nil {
		return x.ReStartLostTask
	}
	return false
}

func (x *App) GetMaxStartRetry() int32 {
	if x != nil {
		return x.MaxStartRetry
	}
	return 0
}

func (x *App) GetExitWaitSecond() int32 {
	if x != nil {
		return x.ExitWaitSecond
	}
	return 0
}

func (x *App) GetNetworkCard() string {
	if x != nil {
		return x.NetworkCard
	}
	return ""
}

func (x *App) GetIsQuotaAlarm() bool {
	if x != nil {
		return x.IsQuotaAlarm
	}
	return false
}

func (x *App) GetUuidFromIp() bool {
	if x != nil {
		return x.UuidFromIp
	}
	return false
}

func (x *App) GetCollectProcessCmdPrefix() string {
	if x != nil {
		return x.CollectProcessCmdPrefix
	}
	return ""
}

func (x *App) GetCollectDiskParentDir() string {
	if x != nil {
		return x.CollectDiskParentDir
	}
	return ""
}

func (x *App) GetCollectFrequency() int32 {
	if x != nil {
		return x.CollectFrequency
	}
	return 0
}

func (x *App) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *App) GetGpuThresholdEnable() bool {
	if x != nil {
		return x.GpuThresholdEnable
	}
	return false
}

func (x *App) GetGpuThreshold() string {
	if x != nil {
		return x.GpuThreshold
	}
	return ""
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId            string `protobuf:"bytes,1,opt,name=modelId,proto3" json:"modelId,omitempty"`
	ResultSaveBound    int32  `protobuf:"varint,2,opt,name=resultSaveBound,proto3" json:"resultSaveBound,omitempty"`
	MaxResultSize      int32  `protobuf:"varint,3,opt,name=maxResultSize,proto3" json:"maxResultSize,omitempty"`
	ResultSaveProtocal string `protobuf:"bytes,4,opt,name=resultSaveProtocal,proto3" json:"resultSaveProtocal,omitempty"`
	MppMountModels     string `protobuf:"bytes,5,opt,name=mppMountModels,proto3" json:"mppMountModels,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Model) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *Model) GetResultSaveBound() int32 {
	if x != nil {
		return x.ResultSaveBound
	}
	return 0
}

func (x *Model) GetMaxResultSize() int32 {
	if x != nil {
		return x.MaxResultSize
	}
	return 0
}

func (x *Model) GetResultSaveProtocal() string {
	if x != nil {
		return x.ResultSaveProtocal
	}
	return ""
}

func (x *Model) GetMppMountModels() string {
	if x != nil {
		return x.MppMountModels
	}
	return ""
}

type OSS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	Endpoint     string `protobuf:"bytes,2,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	BucketName   string `protobuf:"bytes,3,opt,name=bucketName,proto3" json:"bucketName,omitempty"`
	AccessId     string `protobuf:"bytes,4,opt,name=accessId,proto3" json:"accessId,omitempty"`
	AccessSecret string `protobuf:"bytes,5,opt,name=accessSecret,proto3" json:"accessSecret,omitempty"`
}

func (x *OSS) Reset() {
	*x = OSS{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OSS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OSS) ProtoMessage() {}

func (x *OSS) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OSS.ProtoReflect.Descriptor instead.
func (*OSS) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *OSS) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *OSS) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *OSS) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *OSS) GetAccessId() string {
	if x != nil {
		return x.AccessId
	}
	return ""
}

func (x *OSS) GetAccessSecret() string {
	if x != nil {
		return x.AccessSecret
	}
	return ""
}

type RemoteMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metadata map[string]string `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RemoteMetadata) Reset() {
	*x = RemoteMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteMetadata) ProtoMessage() {}

func (x *RemoteMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteMetadata.ProtoReflect.Descriptor instead.
func (*RemoteMetadata) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *RemoteMetadata) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type RemoteConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items map[string]*RemoteMetadata `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RemoteConfigs) Reset() {
	*x = RemoteConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteConfigs) ProtoMessage() {}

func (x *RemoteConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteConfigs.ProtoReflect.Descriptor instead.
func (*RemoteConfigs) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *RemoteConfigs) GetItems() map[string]*RemoteMetadata {
	if x != nil {
		return x.Items
	}
	return nil
}

type DataHubConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataHubRemoteConfig *DataHubRemoteConfig `protobuf:"bytes,1,opt,name=dataHubRemoteConfig,proto3" json:"dataHubRemoteConfig,omitempty"`
}

func (x *DataHubConfig) Reset() {
	*x = DataHubConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataHubConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataHubConfig) ProtoMessage() {}

func (x *DataHubConfig) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataHubConfig.ProtoReflect.Descriptor instead.
func (*DataHubConfig) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *DataHubConfig) GetDataHubRemoteConfig() *DataHubRemoteConfig {
	if x != nil {
		return x.DataHubRemoteConfig
	}
	return nil
}

type DataHubRemoteConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MbsDfOssEndpoint       string `protobuf:"bytes,1,opt,name=mbsDfOssEndpoint,proto3" json:"mbsDfOssEndpoint,omitempty"`
	MbsDfOssBucket         string `protobuf:"bytes,2,opt,name=mbsDfOssBucket,proto3" json:"mbsDfOssBucket,omitempty"`
	MbsRedisHost           string `protobuf:"bytes,3,opt,name=mbsRedisHost,proto3" json:"mbsRedisHost,omitempty"`
	MbsRedisPwd            string `protobuf:"bytes,4,opt,name=mbsRedisPwd,proto3" json:"mbsRedisPwd,omitempty"`
	MbsDstChannelSize      int32  `protobuf:"varint,5,opt,name=mbsDstChannelSize,proto3" json:"mbsDstChannelSize,omitempty"`
	MbsDstChannelExpired   int32  `protobuf:"varint,6,opt,name=mbsDstChannelExpired,proto3" json:"mbsDstChannelExpired,omitempty"`
	MbsCenterEndpoint      string `protobuf:"bytes,7,opt,name=mbsCenterEndpoint,proto3" json:"mbsCenterEndpoint,omitempty"`
	MbsRedisPool           int32  `protobuf:"varint,8,opt,name=mbsRedisPool,proto3" json:"mbsRedisPool,omitempty"`
	MbsRedisTimeout        int32  `protobuf:"varint,9,opt,name=mbsRedisTimeout,proto3" json:"mbsRedisTimeout,omitempty"`
	MbsCenterMyifname      string `protobuf:"bytes,10,opt,name=mbsCenterMyifname,proto3" json:"mbsCenterMyifname,omitempty"`
	MbsDstSessionTimeout   int32  `protobuf:"varint,11,opt,name=mbsDstSessionTimeout,proto3" json:"mbsDstSessionTimeout,omitempty"`
	MbsDfOssAkId           string `protobuf:"bytes,12,opt,name=mbsDfOssAkId,proto3" json:"mbsDfOssAkId,omitempty"`
	MbsDfOssAkSecret       string `protobuf:"bytes,13,opt,name=mbsDfOssAkSecret,proto3" json:"mbsDfOssAkSecret,omitempty"`
	MbsDfOssUrlExpiredSpan int32  `protobuf:"varint,14,opt,name=mbsDfOssUrlExpiredSpan,proto3" json:"mbsDfOssUrlExpiredSpan,omitempty"`
}

func (x *DataHubRemoteConfig) Reset() {
	*x = DataHubRemoteConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataHubRemoteConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataHubRemoteConfig) ProtoMessage() {}

func (x *DataHubRemoteConfig) ProtoReflect() protoreflect.Message {
	mi := &file_agent_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataHubRemoteConfig.ProtoReflect.Descriptor instead.
func (*DataHubRemoteConfig) Descriptor() ([]byte, []int) {
	return file_agent_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *DataHubRemoteConfig) GetMbsDfOssEndpoint() string {
	if x != nil {
		return x.MbsDfOssEndpoint
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsDfOssBucket() string {
	if x != nil {
		return x.MbsDfOssBucket
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsRedisHost() string {
	if x != nil {
		return x.MbsRedisHost
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsRedisPwd() string {
	if x != nil {
		return x.MbsRedisPwd
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsDstChannelSize() int32 {
	if x != nil {
		return x.MbsDstChannelSize
	}
	return 0
}

func (x *DataHubRemoteConfig) GetMbsDstChannelExpired() int32 {
	if x != nil {
		return x.MbsDstChannelExpired
	}
	return 0
}

func (x *DataHubRemoteConfig) GetMbsCenterEndpoint() string {
	if x != nil {
		return x.MbsCenterEndpoint
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsRedisPool() int32 {
	if x != nil {
		return x.MbsRedisPool
	}
	return 0
}

func (x *DataHubRemoteConfig) GetMbsRedisTimeout() int32 {
	if x != nil {
		return x.MbsRedisTimeout
	}
	return 0
}

func (x *DataHubRemoteConfig) GetMbsCenterMyifname() string {
	if x != nil {
		return x.MbsCenterMyifname
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsDstSessionTimeout() int32 {
	if x != nil {
		return x.MbsDstSessionTimeout
	}
	return 0
}

func (x *DataHubRemoteConfig) GetMbsDfOssAkId() string {
	if x != nil {
		return x.MbsDfOssAkId
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsDfOssAkSecret() string {
	if x != nil {
		return x.MbsDfOssAkSecret
	}
	return ""
}

func (x *DataHubRemoteConfig) GetMbsDfOssUrlExpiredSpan() int32 {
	if x != nil {
		return x.MbsDfOssUrlExpiredSpan
	}
	return 0
}

var File_agent_conf_conf_proto protoreflect.FileDescriptor

var file_agent_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xc1, 0x02, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74,
	0x72, 0x61, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x70,
	0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x06, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x03, 0x6f, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x4f, 0x53, 0x53, 0x52, 0x03, 0x6f, 0x73, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x22, 0x5b, 0x0a, 0x07, 0x44, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x42, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xa2, 0x03, 0x0a, 0x06, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a,
	0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x68, 0x65, 0x6c, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x69, 0x6e,
	0x57, 0x61, 0x69, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x57, 0x61, 0x69, 0x74, 0x53, 0x68, 0x65, 0x6c,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x55, 0x72,
	0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xce, 0x01, 0x0a, 0x08, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0f, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49,
	0x73, 0x4f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6e, 0x61, 0x63, 0x6f, 0x73,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x73, 0x4f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x63, 0x6f, 0x73, 0x41, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x63, 0x6f, 0x73, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x63, 0x6f,
	0x73, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x61, 0x63,
	0x6f, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x69, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x69,
	0x22, 0x83, 0x06, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f,
	0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x64, 0x49,
	0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x70, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x12, 0x2c,
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64,
	0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x69, 0x74, 0x57, 0x61, 0x69, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x78, 0x69, 0x74,
	0x57, 0x61, 0x69, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x4c, 0x6f, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x73, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x69, 0x74,
	0x57, 0x61, 0x69, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x65, 0x78, 0x69, 0x74, 0x57, 0x61, 0x69, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x75, 0x69, 0x64, 0x46, 0x72,
	0x6f, 0x6d, 0x49, 0x70, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x55, 0x75, 0x69, 0x64,
	0x46, 0x72, 0x6f, 0x6d, 0x49, 0x70, 0x12, 0x38, 0x0a, 0x17, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6d, 0x64, 0x50, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6d, 0x64, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x32, 0x0a, 0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x44, 0x69, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x46,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74,
	0x61, 0x67, 0x12, 0x2e, 0x0a, 0x12, 0x67, 0x70, 0x75, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x67, 0x70, 0x75, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x70, 0x75, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x70, 0x75, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x53, 0x61, 0x76, 0x65, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x61, 0x76, 0x65, 0x42,
	0x6f, 0x75, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x53, 0x61, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x61, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x61,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x70,
	0x70, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6d, 0x70, 0x70, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x03, 0x4f, 0x53, 0x53, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x95,
	0x01, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x46, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa5, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x56, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64,
	0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x48, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x53, 0x0a, 0x13, 0x64, 0x61, 0x74, 0x61, 0x48, 0x75, 0x62, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x75, 0x62, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x13, 0x64, 0x61, 0x74, 0x61, 0x48, 0x75, 0x62, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0xf7, 0x04, 0x0a, 0x13, 0x44, 0x61, 0x74, 0x61, 0x48, 0x75, 0x62,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x10,
	0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f, 0x73, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f, 0x73, 0x73,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x62, 0x73, 0x44,
	0x66, 0x4f, 0x73, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f, 0x73, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x62, 0x73, 0x52, 0x65, 0x64, 0x69, 0x73, 0x48, 0x6f, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x62, 0x73, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x48, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x62, 0x73, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x50, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x62, 0x73, 0x52, 0x65,
	0x64, 0x69, 0x73, 0x50, 0x77, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x11, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x14, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x62, 0x73, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x62, 0x73, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x62, 0x73, 0x52, 0x65, 0x64,
	0x69, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x62,
	0x73, 0x52, 0x65, 0x64, 0x69, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x62,
	0x73, 0x52, 0x65, 0x64, 0x69, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x62, 0x73, 0x52, 0x65, 0x64, 0x69, 0x73, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x62, 0x73, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x4d, 0x79, 0x69, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6d, 0x62, 0x73, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x4d, 0x79, 0x69, 0x66, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x14, 0x6d, 0x62, 0x73, 0x44, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f,
	0x73, 0x73, 0x41, 0x6b, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x62,
	0x73, 0x44, 0x66, 0x4f, 0x73, 0x73, 0x41, 0x6b, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x62,
	0x73, 0x44, 0x66, 0x4f, 0x73, 0x73, 0x41, 0x6b, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f, 0x73, 0x73, 0x41, 0x6b,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x16, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f,
	0x73, 0x73, 0x55, 0x72, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x53, 0x70, 0x61, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6d, 0x62, 0x73, 0x44, 0x66, 0x4f, 0x73, 0x73,
	0x55, 0x72, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x53, 0x70, 0x61, 0x6e, 0x42, 0x1e,
	0x5a, 0x1c, 0x6d, 0x70, 0x70, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_conf_conf_proto_rawDescOnce sync.Once
	file_agent_conf_conf_proto_rawDescData = file_agent_conf_conf_proto_rawDesc
)

func file_agent_conf_conf_proto_rawDescGZIP() []byte {
	file_agent_conf_conf_proto_rawDescOnce.Do(func() {
		file_agent_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_conf_conf_proto_rawDescData)
	})
	return file_agent_conf_conf_proto_rawDescData
}

var file_agent_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_agent_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),           // 0: agent.config.Bootstrap
	(*Datahub)(nil),             // 1: agent.config.Datahub
	(*Server)(nil),              // 2: agent.config.Server
	(*Engine)(nil),              // 3: agent.config.Engine
	(*Schedule)(nil),            // 4: agent.config.Schedule
	(*App)(nil),                 // 5: agent.config.App
	(*Model)(nil),               // 6: agent.config.Model
	(*OSS)(nil),                 // 7: agent.config.OSS
	(*RemoteMetadata)(nil),      // 8: agent.config.RemoteMetadata
	(*RemoteConfigs)(nil),       // 9: agent.config.RemoteConfigs
	(*DataHubConfig)(nil),       // 10: agent.config.DataHubConfig
	(*DataHubRemoteConfig)(nil), // 11: agent.config.DataHubRemoteConfig
	nil,                         // 12: agent.config.RemoteMetadata.MetadataEntry
	nil,                         // 13: agent.config.RemoteConfigs.ItemsEntry
}
var file_agent_conf_conf_proto_depIdxs = []int32{
	2,  // 0: agent.config.Bootstrap.server:type_name -> agent.config.Server
	5,  // 1: agent.config.Bootstrap.app:type_name -> agent.config.App
	3,  // 2: agent.config.Bootstrap.engine:type_name -> agent.config.Engine
	4,  // 3: agent.config.Bootstrap.schedule:type_name -> agent.config.Schedule
	6,  // 4: agent.config.Bootstrap.model:type_name -> agent.config.Model
	7,  // 5: agent.config.Bootstrap.oss:type_name -> agent.config.OSS
	1,  // 6: agent.config.Bootstrap.datahub:type_name -> agent.config.Datahub
	12, // 7: agent.config.RemoteMetadata.metadata:type_name -> agent.config.RemoteMetadata.MetadataEntry
	13, // 8: agent.config.RemoteConfigs.items:type_name -> agent.config.RemoteConfigs.ItemsEntry
	11, // 9: agent.config.DataHubConfig.dataHubRemoteConfig:type_name -> agent.config.DataHubRemoteConfig
	8,  // 10: agent.config.RemoteConfigs.ItemsEntry.value:type_name -> agent.config.RemoteMetadata
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_agent_conf_conf_proto_init() }
func file_agent_conf_conf_proto_init() {
	if File_agent_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_agent_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Datahub); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Engine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OSS); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataHubConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataHubRemoteConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_conf_conf_proto_goTypes,
		DependencyIndexes: file_agent_conf_conf_proto_depIdxs,
		MessageInfos:      file_agent_conf_conf_proto_msgTypes,
	}.Build()
	File_agent_conf_conf_proto = out.File
	file_agent_conf_conf_proto_rawDesc = nil
	file_agent_conf_conf_proto_goTypes = nil
	file_agent_conf_conf_proto_depIdxs = nil
}
