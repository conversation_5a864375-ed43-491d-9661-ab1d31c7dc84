package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"

	"github.com/cinience/animus/datastores/gormhelper"

	"github.com/cinience/animus/safe"
	"github.com/glebarez/sqlite"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/opentelemetry/tracing"
)

// Data .
type Data struct {
	db *gorm.DB

	mainDb        *gorm.DB
	backupDb      *gorm.DB
	activeDb      string
	failCount     int32
	successCount  int32
	threshold     int32
	checkInterval time.Duration
	dbConfig      map[string]interface{}
	mu            sync.RWMutex
}

type BackUpDatabase struct {
	db *gorm.DB
}

func (d *Data) GetDB() *gorm.DB {
	return d.db
}

// NewData .
func NewData(logger log.Logger, db *gorm.DB, dbBackup *BackUpDatabase, c *conf.Data) (*Data, func(), error) {
	cleanup := func() {
		log.NewHelper(logger).Info("closing the data resources")
	}

	// 兼容无双库场景
	if dbBackup == nil || dbBackup.db == nil {
		return &Data{
			db:       db,
			mainDb:   db,
			activeDb: "mainDb",
		}, cleanup, nil
	}

	log.Info("tasker-manager init with double db")
	data := &Data{
		db:            db,
		mainDb:        db,
		backupDb:      dbBackup.db,
		activeDb:      "mainDb",
		threshold:     c.Threshold,
		checkInterval: 2 * time.Second,
	}
	safe.Go(data.StartHealthCheck)
	safe.Go(data.LoadForceConfig)

	return data, cleanup, nil
}

func NewDB(c *conf.Data, l *conf.LiveTransCodeConfig) (*gorm.DB, error) {
	log.Infof("tasker-manager init db. info:%v", c.Database)

	var db *gorm.DB
	var err error
	driver := strings.ToLower(c.Database.Driver)
	switch driver {
	case "mysql":
		db, err = gorm.Open(mysql.Open(c.Database.Source), &gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Error),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(c.Database.Source), &gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Error),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	default:
		err = fmt.Errorf("unsupported driver %s", driver)
	}

	if err != nil {
		log.Fatalf("failed opening connection to mysql: %v", err)
		return nil, err
	}

	if err := db.Use(tracing.NewPlugin()); err != nil {
		log.Fatalf("failed open tracing to mysql: %v", err)
		return nil, err
	}

	log.Info("start configDB ....")
	if err := configDB(db); err != nil {
		log.Fatalf("failed config to mysql: %v", err)
		return nil, err
	}

	log.Info("start registerModel ....")
	if l != nil {
		log.Infof("start registerModel of live ....")
	}
	registerModel(db, l != nil)

	if c.Database.AutoMigrate {
		log.Warn("start initTables, this operation is dangerous ....")
	}
	// 初始化数据库
	//
	//	1.AutoMigrate为true: 自动更新数据库结构（需确认是否存在锁表风险）
	//	2.AutoMigrate为false:
	//		a.如果数据库表存在：不更新数据库结构，检查数据库完整性，如果数据库结构不一致则报错，应用无法启动
	//		b.如果数据库表不存在：创建数据库表，并更新数据库结构
	initTables(db, c.Database.AutoMigrate, l != nil)

	return db, nil
}

func registerModel(db *gorm.DB, live bool) {
	db.Model(&domain.Task{})
	db.Model(&domain.Leader{})
	db.Model(&domain.Tenant{})
	db.Model(&domain.Category{})
	db.Model(&domain.AsyncTaskPipeline{})
	db.Model(&domain.TaskSentinelConfig{})
	db.Model(&domain.KVStore{})
	db.Model(&domain.MixGrayConfig{})
	if live {
		db.Model(&domain.TranscodeAttachment{})
		db.Model(&domain.SwitchConfig{})
		db.Model(&domain.SourceStreamTask{})
		db.Model(&domain.UnitTask{})
		db.Model(&domain.TranscodeStatInfo{})
	}
}

func initTables(db *gorm.DB, forceMigrate bool, live bool) {
	if err := gormhelper.AutoMigrateWithConfig(db.Table(models.StreamTaskTableName), forceMigrate, &domain.Task{}); err != nil {
		log.Fatalf("failed to create table tasks: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db.Table(models.BatchTaskTableName), forceMigrate, &domain.Task{}); err != nil {
		log.Fatalf("failed to create table tasks: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.Leader{}); err != nil {
		log.Fatalf("failed to create table leader: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.Tenant{}); err != nil {
		log.Fatalf("failed to create table tenant: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.Category{}); err != nil {
		log.Fatalf("failed to create table category: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.AsyncTaskPipeline{}); err != nil {
		log.Fatalf("failed to create table async_task_pipeline: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.TaskSentinelConfig{}); err != nil {
		log.Fatalf("failed to create table task_sentinel_config: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.KVStore{}); err != nil {
		log.Fatalf("failed to create table kv_store: %v", err)
	}

	if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.MixGrayConfig{}); err != nil {
		log.Fatalf("failed to create table mix_gray_config: %v", err)
	}

	if live {
		if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.TranscodeAttachment{}); err != nil {
			log.Fatalf("failed to create table transcode_attachment: %v", err)
		}

		if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.SourceStreamTask{}); err != nil {
			log.Fatalf("failed to create table source_stream_task: %v", err)
		}

		if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.SwitchConfig{}); err != nil {
			log.Fatalf("failed to create table switch_config: %v", err)
		}

		if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.UnitTask{}); err != nil {
			log.Fatalf("failed to create table unit_task: %v", err)
		}

		if err := gormhelper.AutoMigrateWithConfig(db, forceMigrate, &domain.TranscodeStatInfo{}); err != nil {
			log.Fatalf("failed to create table transcode_stat_info: %v", err)
		}
	}

}

func configDB(db *gorm.DB) error {
	sqlDb, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to config db: %v", err)
	}
	// 设置连接池
	// 空闲
	sqlDb.SetMaxIdleConns(50)
	// 打开
	sqlDb.SetMaxOpenConns(100)
	// 超时
	sqlDb.SetConnMaxLifetime(time.Second * 2)

	return nil
}

func NewDbBackup(c *conf.Data, l *conf.LiveTransCodeConfig) (*BackUpDatabase, error) {
	log.Infof("tasker-manager init backup db. info:%v", c.DatabaseBackup)

	var db *gorm.DB
	var err error
	dbBackup := &BackUpDatabase{
		db: nil,
	}
	if c.DatabaseBackup == nil {
		return dbBackup, nil
	}
	if c.DatabaseBackup.Driver == "" {
		return dbBackup, nil
	}
	driver := strings.ToLower(c.DatabaseBackup.Driver)
	switch driver {
	case "mysql":
		db, err = gorm.Open(mysql.Open(c.DatabaseBackup.Source), &gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Error),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(c.DatabaseBackup.Source), &gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Error),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	case "postgresql":
		db, err = gorm.Open(postgres.Open(c.DatabaseBackup.Source), &gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Error),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	default:
		err = fmt.Errorf("unsupported db backup driver %s", driver)
	}

	if err != nil {
		log.Fatalf("failed opening connection to backup mysql: %v", err)
		return nil, err
	}

	if err := db.Use(tracing.NewPlugin()); err != nil {
		log.Fatalf("failed open tracing to backup mysql: %v", err)
		return nil, err
	}

	log.Info("start configBackupDB ....")
	if err := configDB(db); err != nil {
		log.Fatalf("failed config to backup mysql: %v", err)
		return nil, err
	}

	log.Info("start registerModel ....")
	registerModel(db, l != nil)

	if c.DatabaseBackup.AutoMigrate {
		log.Warn("start initTables, this operation is dangerous ....")
		initTables(db, c.DatabaseBackup.AutoMigrate, l != nil)
	}

	return &BackUpDatabase{
		db: db,
	}, nil
}

// StartHealthCheck 启动定期健康检查
func (d *Data) StartHealthCheck() {
	ticker := time.NewTicker(d.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			d.healthCheck()
		}
	}
}

// 增加kv配置，当配置是'{"db":"mainDb","force":true}' 时表示人工强制指定，不可替换，false表示可以故障自动切换
func (d *Data) healthCheck() {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.dbConfig != nil && d.dbConfig["force"].(bool) == true {
		log.Warnf("force use db :%s", d.dbConfig["db"].(string))
		if d.activeDb != d.dbConfig["db"].(string) {
			log.Warnf("force use db :%s, current active db: %s", d.dbConfig["db"].(string), d.activeDb)
			d.switchDB(true)
		}
		return
	}

	// 如果当前使用主库
	if d.activeDb == "mainDb" {
		// 检查 MySQL 健康状态
		if err := d.pingMainDB(); err != nil {
			d.failCount++
			log.Errorf("mianDb health check failed (%d/%d): %v", d.failCount, d.threshold, err)

			// 达到阈值，切换到 备库
			if d.failCount >= d.threshold {
				if errNewDb := d.pingBackupDB(); errNewDb == nil {
					log.Warnf("Switching from mainDB to backupDB")
					d.switchDB(false)
				} else {
					log.Errorf("backupDB is also unavailable: %v", err)
				}
			}
		} else {
			// 重置失败计数
			if d.failCount > 0 {
				log.Infof("mainDB is healthy again, resetting fail count")
				d.failCount = 0
			}
		}
	} else {
		// 当前使用 backupDB，尝试切回 mainDB
		if err := d.pingMainDB(); err == nil {
			d.successCount++
		} else {
			d.successCount = 0
			log.Warnf("mainDB still unavailable:%v", err)
		}

		// 达到阈值，切换回主库
		if d.successCount >= d.threshold {
			log.Warnf("mainDB is available again, switching back from backupDB")
			d.switchDB(false)
		}
	}
}

// pingMainDb 检查 主库 连接
func (d *Data) pingMainDB() error {
	sqlDB, _ := d.mainDb.DB()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	return sqlDB.PingContext(ctx)
}

// pingBackupDb 检查 备库 连接
func (d *Data) pingBackupDB() error {
	sqlDB, _ := d.backupDb.DB()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	return sqlDB.PingContext(ctx)
}

func (d *Data) switchDB(force bool) {
	if d.activeDb == "mainDb" {
		d.activeDb = "backupDb"
		d.db = d.backupDb
		d.failCount = 0
		d.successCount = 0
		d.setKvConfig("backupDb", force)
	} else {
		d.activeDb = "mainDb"
		d.db = d.mainDb
		d.failCount = 0
		d.successCount = 0
		d.setKvConfig("mainDb", force)
	}
	log.Warnf("current active db: %s, db msg:%+v", d.activeDb, d.db)
}

func (d *Data) LoadForceConfig() {
	ticker := time.NewTicker(d.checkInterval * 60)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			d.loadConfig()
		}
	}
}

func (d *Data) loadConfig() {
	d.mu.Lock()
	defer d.mu.Unlock()
	log.Infof("load db force config begin")
	config := d.getKvConfig()
	if config != nil {
		log.Infof("load db force config:%+v", config)
		d.dbConfig = config
	}
}

func (d *Data) setKvConfig(db string, force bool) {
	log.Infof("set kvstore config begin, db:%s, force:%v", db, force)
	value := map[string]interface{}{
		"db":    db,
		"force": force,
	}

	jsonData, err := json.Marshal(value)
	if err != nil {
		log.Errorf("Error marshaling to JSON:%v", err)
		return
	}

	kv := &domain.KVStore{
		Repo:        "params",
		Key:         "backupDbConfig",
		Value:       string(jsonData),
		ExpiredTime: time.Now().Add(100 * 365 * 24 * time.Hour),
	}

	result := d.db.Exec(
		"INSERT INTO kv_stores(gmt_create, gmt_modified, repo, `key`, value, gmt_expired) "+
			"VALUES(now(), now(), ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE value = ?, gmt_expired = ?, gmt_modified = now()",
		kv.Repo, kv.Key, kv.Value, kv.ExpiredTime, kv.Value, kv.ExpiredTime,
	)
	if result.Error != nil {
		log.Warnf("set kvstore failed, err:%v", result)
	}
	log.Infof("set kvstore success, kv:%+v", kv)
}

func (d *Data) getKvConfig() map[string]interface{} {
	kv := &domain.KVStore{
		Repo: "params",
		Key:  "backupDbConfig",
	}
	result := d.db.Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", kv.Repo, kv.Key).Take(&kv)
	if result.Error != nil {
		log.Warnf("get kvstore failed, err:%v", result)
		// 增加备库查询
		if d.activeDb == "mainDb" {
			result = d.backupDb.Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", kv.Repo, kv.Key).Take(&kv)
		} else {
			result = d.mainDb.Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", kv.Repo, kv.Key).Take(&kv)
		}
		if result.Error != nil {
			log.Warnf("get kvstore from backupDb failed, err:%v", result)
			return nil
		}
	}

	var value map[string]interface{}
	err := json.Unmarshal([]byte(kv.Value), &value)
	if err != nil {
		log.Errorf("Error unmarshaling from JSON:%v", err)
		return nil
	}
	return value
}
