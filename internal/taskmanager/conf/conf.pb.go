// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.29.0
// source: taskmanager/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server                *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Registry              *Registry              `protobuf:"bytes,2,opt,name=registry,proto3" json:"registry,omitempty"`
	Data                  *Data                  `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Swagger               *Swagger               `protobuf:"bytes,4,opt,name=swagger,proto3" json:"swagger,omitempty"`
	Notifier              *Notifier              `protobuf:"bytes,5,opt,name=notifier,proto3" json:"notifier,omitempty"`
	Cluster               *Cluster               `protobuf:"bytes,6,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Mns                   *Mns                   `protobuf:"bytes,7,opt,name=mns,proto3" json:"mns,omitempty"`
	Router                *Router                `protobuf:"bytes,8,opt,name=router,proto3" json:"router,omitempty"`
	Quota                 *Quota                 `protobuf:"bytes,9,opt,name=quota,proto3" json:"quota,omitempty"`
	DefaultSentinelConfig *DefaultSentinelConfig `protobuf:"bytes,10,opt,name=defaultSentinelConfig,proto3" json:"defaultSentinelConfig,omitempty"`
	// 三元组调度配置
	ManagerConfig       *ManagerConfig       `protobuf:"bytes,11,opt,name=managerConfig,proto3" json:"managerConfig,omitempty"`
	Workflow            *Workflow            `protobuf:"bytes,12,opt,name=workflow,proto3" json:"workflow,omitempty"`
	LiveTransCodeConfig *LiveTransCodeConfig `protobuf:"bytes,13,opt,name=liveTransCodeConfig,proto3" json:"liveTransCodeConfig,omitempty"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetRegistry() *Registry {
	if x != nil {
		return x.Registry
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetSwagger() *Swagger {
	if x != nil {
		return x.Swagger
	}
	return nil
}

func (x *Bootstrap) GetNotifier() *Notifier {
	if x != nil {
		return x.Notifier
	}
	return nil
}

func (x *Bootstrap) GetCluster() *Cluster {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *Bootstrap) GetMns() *Mns {
	if x != nil {
		return x.Mns
	}
	return nil
}

func (x *Bootstrap) GetRouter() *Router {
	if x != nil {
		return x.Router
	}
	return nil
}

func (x *Bootstrap) GetQuota() *Quota {
	if x != nil {
		return x.Quota
	}
	return nil
}

func (x *Bootstrap) GetDefaultSentinelConfig() *DefaultSentinelConfig {
	if x != nil {
		return x.DefaultSentinelConfig
	}
	return nil
}

func (x *Bootstrap) GetManagerConfig() *ManagerConfig {
	if x != nil {
		return x.ManagerConfig
	}
	return nil
}

func (x *Bootstrap) GetWorkflow() *Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

func (x *Bootstrap) GetLiveTransCodeConfig() *LiveTransCodeConfig {
	if x != nil {
		return x.LiveTransCodeConfig
	}
	return nil
}

type LiveTransCodeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unit         string                              `protobuf:"bytes,1,opt,name=unit,proto3" json:"unit,omitempty"`
	Env          string                              `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
	BizHost      string                              `protobuf:"bytes,3,opt,name=bizHost,proto3" json:"bizHost,omitempty"`
	GslbHost     string                              `protobuf:"bytes,4,opt,name=gslbHost,proto3" json:"gslbHost,omitempty"`
	LiveTocHost  string                              `protobuf:"bytes,5,opt,name=liveTocHost,proto3" json:"liveTocHost,omitempty"`
	UnitDatabase []*LiveTransCodeConfig_UnitDatabase `protobuf:"bytes,6,rep,name=unitDatabase,proto3" json:"unitDatabase,omitempty"`
}

func (x *LiveTransCodeConfig) Reset() {
	*x = LiveTransCodeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveTransCodeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveTransCodeConfig) ProtoMessage() {}

func (x *LiveTransCodeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveTransCodeConfig.ProtoReflect.Descriptor instead.
func (*LiveTransCodeConfig) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *LiveTransCodeConfig) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *LiveTransCodeConfig) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *LiveTransCodeConfig) GetBizHost() string {
	if x != nil {
		return x.BizHost
	}
	return ""
}

func (x *LiveTransCodeConfig) GetGslbHost() string {
	if x != nil {
		return x.GslbHost
	}
	return ""
}

func (x *LiveTransCodeConfig) GetLiveTocHost() string {
	if x != nil {
		return x.LiveTocHost
	}
	return ""
}

func (x *LiveTransCodeConfig) GetUnitDatabase() []*LiveTransCodeConfig_UnitDatabase {
	if x != nil {
		return x.UnitDatabase
	}
	return nil
}

type Quota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host              string             `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	EngineQuotaSetMap *EngineQuotaSetMap `protobuf:"bytes,2,opt,name=engineQuotaSetMap,proto3" json:"engineQuotaSetMap,omitempty"`
}

func (x *Quota) Reset() {
	*x = Quota{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Quota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Quota) ProtoMessage() {}

func (x *Quota) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Quota.ProtoReflect.Descriptor instead.
func (*Quota) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Quota) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Quota) GetEngineQuotaSetMap() *EngineQuotaSetMap {
	if x != nil {
		return x.EngineQuotaSetMap
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http       *Server_HTTP `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc       *Server_GRPC `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	TracingUrl string       `protobuf:"bytes,3,opt,name=tracingUrl,proto3" json:"tracingUrl,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetTracingUrl() string {
	if x != nil {
		return x.TracingUrl
	}
	return ""
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database       *Data_Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis          *Data_Redis    `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	DatabaseBackup *Data_Database `protobuf:"bytes,3,opt,name=databaseBackup,proto3" json:"databaseBackup,omitempty"`
	Threshold      int32          `protobuf:"varint,4,opt,name=threshold,proto3" json:"threshold,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetDatabaseBackup() *Data_Database {
	if x != nil {
		return x.DatabaseBackup
	}
	return nil
}

func (x *Data) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

type Registry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint string `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *Registry) Reset() {
	*x = Registry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Registry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Registry) ProtoMessage() {}

func (x *Registry) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Registry.ProtoReflect.Descriptor instead.
func (*Registry) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *Registry) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type Notifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *Notifier) Reset() {
	*x = Notifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Notifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notifier) ProtoMessage() {}

func (x *Notifier) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notifier.ProtoReflect.Descriptor instead.
func (*Notifier) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Notifier) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Workflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *Workflow) Reset() {
	*x = Workflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow) ProtoMessage() {}

func (x *Workflow) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow.ProtoReflect.Descriptor instead.
func (*Workflow) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *Workflow) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Cluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *Cluster) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Cluster) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type Swagger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled string `protobuf:"bytes,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *Swagger) Reset() {
	*x = Swagger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Swagger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Swagger) ProtoMessage() {}

func (x *Swagger) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Swagger.ProtoReflect.Descriptor instead.
func (*Swagger) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *Swagger) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

type Mns struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessKey string `protobuf:"bytes,1,opt,name=accessKey,proto3" json:"accessKey,omitempty"`
	SecretKey string `protobuf:"bytes,2,opt,name=secretKey,proto3" json:"secretKey,omitempty"`
}

func (x *Mns) Reset() {
	*x = Mns{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mns) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mns) ProtoMessage() {}

func (x *Mns) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mns.ProtoReflect.Descriptor instead.
func (*Mns) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *Mns) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *Mns) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type Router struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Percent       int32  `protobuf:"varint,1,opt,name=percent,proto3" json:"percent,omitempty"`
	OldSystemHost string `protobuf:"bytes,2,opt,name=oldSystemHost,proto3" json:"oldSystemHost,omitempty"`
	// 此字段废弃
	HoldFailed bool `protobuf:"varint,3,opt,name=holdFailed,proto3" json:"holdFailed,omitempty"`
	// ai灰度配置
	OldAiSystemHost string         `protobuf:"bytes,4,opt,name=oldAiSystemHost,proto3" json:"oldAiSystemHost,omitempty"`
	EngineTable     []*EngineTable `protobuf:"bytes,5,rep,name=engineTable,proto3" json:"engineTable,omitempty"`
	OldSystemExist  bool           `protobuf:"varint,6,opt,name=oldSystemExist,proto3" json:"oldSystemExist,omitempty"`
	StopBoth        bool           `protobuf:"varint,7,opt,name=stopBoth,proto3" json:"stopBoth,omitempty"`
	DomainTable     []*DomainTable `protobuf:"bytes,8,rep,name=domainTable,proto3" json:"domainTable,omitempty"`
}

func (x *Router) Reset() {
	*x = Router{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Router) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Router) ProtoMessage() {}

func (x *Router) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Router.ProtoReflect.Descriptor instead.
func (*Router) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *Router) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *Router) GetOldSystemHost() string {
	if x != nil {
		return x.OldSystemHost
	}
	return ""
}

func (x *Router) GetHoldFailed() bool {
	if x != nil {
		return x.HoldFailed
	}
	return false
}

func (x *Router) GetOldAiSystemHost() string {
	if x != nil {
		return x.OldAiSystemHost
	}
	return ""
}

func (x *Router) GetEngineTable() []*EngineTable {
	if x != nil {
		return x.EngineTable
	}
	return nil
}

func (x *Router) GetOldSystemExist() bool {
	if x != nil {
		return x.OldSystemExist
	}
	return false
}

func (x *Router) GetStopBoth() bool {
	if x != nil {
		return x.StopBoth
	}
	return false
}

func (x *Router) GetDomainTable() []*DomainTable {
	if x != nil {
		return x.DomainTable
	}
	return nil
}

// Map: engine -> quota("resourceName" -> "value")
// engine = "$product/$engineModel/$tag"
type EngineQuotaSetMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EngineQuota map[string]*QuotaSet `protobuf:"bytes,1,rep,name=engineQuota,proto3" json:"engineQuota,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *EngineQuotaSetMap) Reset() {
	*x = EngineQuotaSetMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineQuotaSetMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineQuotaSetMap) ProtoMessage() {}

func (x *EngineQuotaSetMap) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineQuotaSetMap.ProtoReflect.Descriptor instead.
func (*EngineQuotaSetMap) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{12}
}

func (x *EngineQuotaSetMap) GetEngineQuota() map[string]*QuotaSet {
	if x != nil {
		return x.EngineQuota
	}
	return nil
}

type QuotaSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Quota map[string]int64 `protobuf:"bytes,1,rep,name=quota,proto3" json:"quota,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *QuotaSet) Reset() {
	*x = QuotaSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuotaSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuotaSet) ProtoMessage() {}

func (x *QuotaSet) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuotaSet.ProtoReflect.Descriptor instead.
func (*QuotaSet) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{13}
}

func (x *QuotaSet) GetQuota() map[string]int64 {
	if x != nil {
		return x.Quota
	}
	return nil
}

// AI根据三元组生成灰度表
type EngineTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product     string `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	EngineModel string `protobuf:"bytes,2,opt,name=engineModel,proto3" json:"engineModel,omitempty"`
	Tag         string `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	Percent     int32  `protobuf:"varint,4,opt,name=percent,proto3" json:"percent,omitempty"`
}

func (x *EngineTable) Reset() {
	*x = EngineTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineTable) ProtoMessage() {}

func (x *EngineTable) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineTable.ProtoReflect.Descriptor instead.
func (*EngineTable) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{14}
}

func (x *EngineTable) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *EngineTable) GetEngineModel() string {
	if x != nil {
		return x.EngineModel
	}
	return ""
}

func (x *EngineTable) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *EngineTable) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

// 根据域名灰度能力
type DomainTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain  string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Percent int32  `protobuf:"varint,2,opt,name=percent,proto3" json:"percent,omitempty"`
}

func (x *DomainTable) Reset() {
	*x = DomainTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DomainTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainTable) ProtoMessage() {}

func (x *DomainTable) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainTable.ProtoReflect.Descriptor instead.
func (*DomainTable) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{15}
}

func (x *DomainTable) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DomainTable) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

// 默认限流配置
type DefaultSentinelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultMaxParallelNum int32 `protobuf:"varint,1,opt,name=defaultMaxParallelNum,proto3" json:"defaultMaxParallelNum,omitempty"`
	Enable                bool  `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *DefaultSentinelConfig) Reset() {
	*x = DefaultSentinelConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DefaultSentinelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultSentinelConfig) ProtoMessage() {}

func (x *DefaultSentinelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultSentinelConfig.ProtoReflect.Descriptor instead.
func (*DefaultSentinelConfig) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{16}
}

func (x *DefaultSentinelConfig) GetDefaultMaxParallelNum() int32 {
	if x != nil {
		return x.DefaultMaxParallelNum
	}
	return 0
}

func (x *DefaultSentinelConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

// 三元组调度配置
// key = $product_$engineModel_$tag 例：rms_rms_default
type ManagerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskManagerConfigMap map[string]*TaskManagerConfig `protobuf:"bytes,1,rep,name=taskManagerConfigMap,proto3" json:"taskManagerConfigMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ManagerConfig) Reset() {
	*x = ManagerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManagerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManagerConfig) ProtoMessage() {}

func (x *ManagerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManagerConfig.ProtoReflect.Descriptor instead.
func (*ManagerConfig) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{17}
}

func (x *ManagerConfig) GetTaskManagerConfigMap() map[string]*TaskManagerConfig {
	if x != nil {
		return x.TaskManagerConfigMap
	}
	return nil
}

// 任务调度配置
type TaskManagerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务重复启动且任务正在运行中时，重入引擎参数不匹配的调度重启策略
	// 1.ignoreAndReturn: 忽略参数不匹配，返回任务启动成功(默认策略)
	// 2.restartAndUpdateParams: 重启任务并更新引擎参数
	TaskRestartExistAndEngineMismatchPolicy string `protobuf:"bytes,1,opt,name=TaskRestartExistAndEngineMismatchPolicy,proto3" json:"TaskRestartExistAndEngineMismatchPolicy,omitempty"`
}

func (x *TaskManagerConfig) Reset() {
	*x = TaskManagerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskManagerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskManagerConfig) ProtoMessage() {}

func (x *TaskManagerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskManagerConfig.ProtoReflect.Descriptor instead.
func (*TaskManagerConfig) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{18}
}

func (x *TaskManagerConfig) GetTaskRestartExistAndEngineMismatchPolicy() string {
	if x != nil {
		return x.TaskRestartExistAndEngineMismatchPolicy
	}
	return ""
}

type LiveTransCodeConfig_UnitDatabase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unit   string `protobuf:"bytes,1,opt,name=unit,proto3" json:"unit,omitempty"`
	Driver string `protobuf:"bytes,2,opt,name=driver,proto3" json:"driver,omitempty"`
	Source string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *LiveTransCodeConfig_UnitDatabase) Reset() {
	*x = LiveTransCodeConfig_UnitDatabase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveTransCodeConfig_UnitDatabase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveTransCodeConfig_UnitDatabase) ProtoMessage() {}

func (x *LiveTransCodeConfig_UnitDatabase) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveTransCodeConfig_UnitDatabase.ProtoReflect.Descriptor instead.
func (*LiveTransCodeConfig_UnitDatabase) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *LiveTransCodeConfig_UnitDatabase) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *LiveTransCodeConfig_UnitDatabase) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *LiveTransCodeConfig_UnitDatabase) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Data_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver      string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source      string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	AutoMigrate bool   `protobuf:"varint,3,opt,name=autoMigrate,proto3" json:"autoMigrate,omitempty"`
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Data_Database) GetAutoMigrate() bool {
	if x != nil {
		return x.AutoMigrate
	}
	return false
}

type Data_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint string `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskmanager_conf_conf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_taskmanager_conf_conf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_taskmanager_conf_conf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Data_Redis) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

var File_taskmanager_conf_conf_proto protoreflect.FileDescriptor

var file_taskmanager_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x73, 0x6b, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x05, 0x0a, 0x09, 0x42, 0x6f,
	0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x07, 0x73,
	0x77, 0x61, 0x67, 0x67, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x77, 0x61, 0x67, 0x67, 0x65,
	0x72, 0x52, 0x07, 0x73, 0x77, 0x61, 0x67, 0x67, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x08, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x52, 0x08, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x03, 0x6d,
	0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x6e, 0x73, 0x52, 0x03, 0x6d, 0x6e, 0x73, 0x12, 0x2a,
	0x0a, 0x06, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x15, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65,
	0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x15, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65,
	0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x0a, 0x0d,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a,
	0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12,
	0x51, 0x0a, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x6c,
	0x69, 0x76, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0xb9, 0x02, 0x0a, 0x13, 0x4c, 0x69, 0x76, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x7a, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x73,
	0x6c, 0x62, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x73,
	0x6c, 0x62, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x54, 0x6f,
	0x63, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x76,
	0x65, 0x54, 0x6f, 0x63, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x69, 0x76, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x55, 0x6e, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x0c, 0x75, 0x6e,
	0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x52, 0x0a, 0x0c, 0x55, 0x6e,
	0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x68,
	0x0a, 0x05, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x11, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53,
	0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x11, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x22, 0xd8, 0x02, 0x0a, 0x06, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70,
	0x12, 0x2b, 0x0a, 0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x12, 0x1e, 0x0a,
	0x0a, 0x74, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x1a, 0x69, 0x0a,
	0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61,
	0x64, 0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0x69, 0x0a, 0x04, 0x47, 0x52, 0x50, 0x43,
	0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64,
	0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33,
	0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x22, 0xcf, 0x02, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69,
	0x73, 0x12, 0x41, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x42, 0x61,
	0x63, 0x6b, 0x75, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x1a, 0x5c, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x1a, 0x23, 0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x26, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x24, 0x0a,
	0x08, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x24, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x31, 0x0a, 0x07, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x23, 0x0a, 0x07,
	0x53, 0x77, 0x61, 0x67, 0x67, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x22, 0x41, 0x0a, 0x03, 0x4d, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x4b, 0x65, 0x79, 0x22, 0xcc, 0x02, 0x0a, 0x06, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x6c, 0x64,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6f, 0x6c, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x6f, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12,
	0x28, 0x0a, 0x0f, 0x6f, 0x6c, 0x64, 0x41, 0x69, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x6f,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x6c, 0x64, 0x41, 0x69, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x6c, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6f, 0x6c,
	0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x74, 0x6f, 0x70, 0x42, 0x6f, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x73, 0x74, 0x6f, 0x70, 0x42, 0x6f, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0b, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x11, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x50, 0x0a, 0x0b, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x2e, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x1a, 0x54, 0x0a, 0x10, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x7b, 0x0a, 0x08, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x35, 0x0a,
	0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53,
	0x65, 0x74, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x71,
	0x75, 0x6f, 0x74, 0x61, 0x1a, 0x38, 0x0a, 0x0a, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x75,
	0x0a, 0x0b, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x3f, 0x0a, 0x0b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x65, 0x0a, 0x15, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x34, 0x0a, 0x15, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72,
	0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c,
	0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xe0, 0x01,
	0x0a, 0x0d, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x67, 0x0a, 0x14, 0x74, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x14, 0x74, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x1a, 0x66, 0x0a, 0x19, 0x54, 0x61, 0x73, 0x6b,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x6d, 0x0a, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x27, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x64, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x27, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x64, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42,
	0x24, 0x5a, 0x22, 0x6d, 0x70, 0x70, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x61, 0x73, 0x6b, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_taskmanager_conf_conf_proto_rawDescOnce sync.Once
	file_taskmanager_conf_conf_proto_rawDescData = file_taskmanager_conf_conf_proto_rawDesc
)

func file_taskmanager_conf_conf_proto_rawDescGZIP() []byte {
	file_taskmanager_conf_conf_proto_rawDescOnce.Do(func() {
		file_taskmanager_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_taskmanager_conf_conf_proto_rawDescData)
	})
	return file_taskmanager_conf_conf_proto_rawDescData
}

var file_taskmanager_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_taskmanager_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),                        // 0: kratos.api.Bootstrap
	(*LiveTransCodeConfig)(nil),              // 1: kratos.api.LiveTransCodeConfig
	(*Quota)(nil),                            // 2: kratos.api.Quota
	(*Server)(nil),                           // 3: kratos.api.Server
	(*Data)(nil),                             // 4: kratos.api.Data
	(*Registry)(nil),                         // 5: kratos.api.Registry
	(*Notifier)(nil),                         // 6: kratos.api.Notifier
	(*Workflow)(nil),                         // 7: kratos.api.Workflow
	(*Cluster)(nil),                          // 8: kratos.api.Cluster
	(*Swagger)(nil),                          // 9: kratos.api.Swagger
	(*Mns)(nil),                              // 10: kratos.api.Mns
	(*Router)(nil),                           // 11: kratos.api.Router
	(*EngineQuotaSetMap)(nil),                // 12: kratos.api.EngineQuotaSetMap
	(*QuotaSet)(nil),                         // 13: kratos.api.QuotaSet
	(*EngineTable)(nil),                      // 14: kratos.api.EngineTable
	(*DomainTable)(nil),                      // 15: kratos.api.DomainTable
	(*DefaultSentinelConfig)(nil),            // 16: kratos.api.DefaultSentinelConfig
	(*ManagerConfig)(nil),                    // 17: kratos.api.ManagerConfig
	(*TaskManagerConfig)(nil),                // 18: kratos.api.taskManagerConfig
	(*LiveTransCodeConfig_UnitDatabase)(nil), // 19: kratos.api.LiveTransCodeConfig.UnitDatabase
	(*Server_HTTP)(nil),                      // 20: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),                      // 21: kratos.api.Server.GRPC
	(*Data_Database)(nil),                    // 22: kratos.api.Data.Database
	(*Data_Redis)(nil),                       // 23: kratos.api.Data.Redis
	nil,                                      // 24: kratos.api.EngineQuotaSetMap.EngineQuotaEntry
	nil,                                      // 25: kratos.api.QuotaSet.QuotaEntry
	nil,                                      // 26: kratos.api.ManagerConfig.TaskManagerConfigMapEntry
	(*durationpb.Duration)(nil),              // 27: google.protobuf.Duration
}
var file_taskmanager_conf_conf_proto_depIdxs = []int32{
	3,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	5,  // 1: kratos.api.Bootstrap.registry:type_name -> kratos.api.Registry
	4,  // 2: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	9,  // 3: kratos.api.Bootstrap.swagger:type_name -> kratos.api.Swagger
	6,  // 4: kratos.api.Bootstrap.notifier:type_name -> kratos.api.Notifier
	8,  // 5: kratos.api.Bootstrap.cluster:type_name -> kratos.api.Cluster
	10, // 6: kratos.api.Bootstrap.mns:type_name -> kratos.api.Mns
	11, // 7: kratos.api.Bootstrap.router:type_name -> kratos.api.Router
	2,  // 8: kratos.api.Bootstrap.quota:type_name -> kratos.api.Quota
	16, // 9: kratos.api.Bootstrap.defaultSentinelConfig:type_name -> kratos.api.DefaultSentinelConfig
	17, // 10: kratos.api.Bootstrap.managerConfig:type_name -> kratos.api.ManagerConfig
	7,  // 11: kratos.api.Bootstrap.workflow:type_name -> kratos.api.Workflow
	1,  // 12: kratos.api.Bootstrap.liveTransCodeConfig:type_name -> kratos.api.LiveTransCodeConfig
	19, // 13: kratos.api.LiveTransCodeConfig.unitDatabase:type_name -> kratos.api.LiveTransCodeConfig.UnitDatabase
	12, // 14: kratos.api.Quota.engineQuotaSetMap:type_name -> kratos.api.EngineQuotaSetMap
	20, // 15: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	21, // 16: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	22, // 17: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	23, // 18: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	22, // 19: kratos.api.Data.databaseBackup:type_name -> kratos.api.Data.Database
	14, // 20: kratos.api.Router.engineTable:type_name -> kratos.api.EngineTable
	15, // 21: kratos.api.Router.domainTable:type_name -> kratos.api.DomainTable
	24, // 22: kratos.api.EngineQuotaSetMap.engineQuota:type_name -> kratos.api.EngineQuotaSetMap.EngineQuotaEntry
	25, // 23: kratos.api.QuotaSet.quota:type_name -> kratos.api.QuotaSet.QuotaEntry
	26, // 24: kratos.api.ManagerConfig.taskManagerConfigMap:type_name -> kratos.api.ManagerConfig.TaskManagerConfigMapEntry
	27, // 25: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	27, // 26: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	13, // 27: kratos.api.EngineQuotaSetMap.EngineQuotaEntry.value:type_name -> kratos.api.QuotaSet
	18, // 28: kratos.api.ManagerConfig.TaskManagerConfigMapEntry.value:type_name -> kratos.api.taskManagerConfig
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_taskmanager_conf_conf_proto_init() }
func file_taskmanager_conf_conf_proto_init() {
	if File_taskmanager_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_taskmanager_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveTransCodeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Quota); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Registry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Notifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Swagger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mns); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Router); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineQuotaSetMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuotaSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DomainTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DefaultSentinelConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManagerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskManagerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveTransCodeConfig_UnitDatabase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Database); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskmanager_conf_conf_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Redis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_taskmanager_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_taskmanager_conf_conf_proto_goTypes,
		DependencyIndexes: file_taskmanager_conf_conf_proto_depIdxs,
		MessageInfos:      file_taskmanager_conf_conf_proto_msgTypes,
	}.Build()
	File_taskmanager_conf_conf_proto = out.File
	file_taskmanager_conf_conf_proto_rawDesc = nil
	file_taskmanager_conf_conf_proto_goTypes = nil
	file_taskmanager_conf_conf_proto_depIdxs = nil
}
