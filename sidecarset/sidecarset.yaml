# sidecarset.yaml
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: agent-sidecarset
spec:
  selector:
    matchLabels:
      mpp.aliyun.com/agent-sidecarset: "true"
  updateStrategy:
    type: RollingUpdate
    maxUnavailable: 1
  containers:
  - name: agent
    image: mpp-pre-registry-vpc.cn-shanghai.cr.aliyuncs.com/mpp/mpp-agent:158106678
    env:
      - name: CSE_INSTANCE_ID
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.uid
      - name: CSE_NODE_NAME
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: spec.nodeName
      - name: CSE_NODE_IP
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: status.hostIP
      - name: CSE_POD_NAME
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.name
      - name: CSE_POD_NAMESPACE
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.namespace
      - name: CSE_POD_IP
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: status.podIP
      - name: MPP_RESOURCE_TYPE
        value: cpu
      - name: MPP_ENGINE_QUOTASET
        value: '{}'
      - name: MPP_ENGINE_URL
        value: 'local:\/\/\/home\/<USER>\/mpp-pub'
      - name: MPP_EXIT_WAIT
        value: 'true'
      - name: MPP_SCHEDULE_URL
        value: 'http:\/\/************:80?version=v1\&gproxy=true'
      - name: IS_SIDECAR
        value: 'true'
      - name: SIGMA_IGNORE_RESOURCE
        value: 'true'
      - name: MPP_CHECK_TERMINATING
        value: 'false'
      - name: APPSTACK_APP_NAME
        value: alirtc-rms
      - name: APPSTACK_ENV_LEVEL
        value: staging2-ncloud
      - name: APPSTACK_ENV_TAG
        value: animus-cn-shanghai
      - name: APPSTACK_REGION
        value: cn-shanghai
      - name: APPSTACK_UNIT
        value: CENTER_UNIT.center
      - name: MPP_REGION
        value: cn-shanghai
      - name: MPP_PRODUCT
        value: rms
      - name: MPP_MODELID
        value: rms
      - name: MPP_TAG
        value: rms_migrate_pre
      - name: SIGMA_APP_NAME
        value: alirtc-rms
      - name: SIGMA_APP_UNIT
        value: CENTER_UNIT.center
      - name: SIGMA_APP_STAGE
        value: PRE_PUBLISH
      - name: SIGMA_APP_GROUP
        value: alirtc-rms_center_animus-cn-shanghai_prehost
      - name: K8S_CONTAINER_NAME
        value: agent
      - name: __ECI_RESOURCE_IGNORE__
        value: 'TRUE'
      - name: SIGMA_APP_SITE
        value: ersi
    resources:
      limits:
        cpu: '1'
        memory: 1Gi
      requests:
        cpu: '1'
        memory: 1Gi
    volumeMounts:
      - name: plugins-dir
        mountPath: /home/<USER>/plugins
      - name: shared-tmp
        mountPath: /tmp
      - name: vol-dir-5
        mountPath: /home/<USER>/agent-pub/A
      - name: vol-dir-6
        mountPath: /home/<USER>/agent-pub/B
      - name: vol-dir-7
        mountPath: /home/<USER>/agent-pub/ver
    livenessProbe:
      exec:
        command:
          - /bin/sh
          - '-c'
          - /home/<USER>/health.sh
          - liveness
      initialDelaySeconds: 30
      timeoutSeconds: 2
      periodSeconds: 5
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      exec:
        command:
          - /bin/sh
          - '-c'
          - /home/<USER>/health.sh
          - readiness
      initialDelaySeconds: 30
      timeoutSeconds: 2
      periodSeconds: 10
      successThreshold: 1
      failureThreshold: 3
    startupProbe:
      exec:
        command:
          - /bin/sh
          - '-c'
          - /home/<USER>/health.sh
          - startup
      initialDelaySeconds: 30
      timeoutSeconds: 2
      periodSeconds: 10
      successThreshold: 1
      failureThreshold: 99
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    imagePullPolicy: Always
  volumes:
    - name: shared-tmp
      emptyDir: {}
    - name: plugins-dir
      emptyDir: {}
    - name: cse-staragent-sn
      downwardAPI:
        items:
          - path: staragent_sn
            fieldRef:
              apiVersion: v1
              fieldPath: 'metadata.labels[''sigma.ali/sn'']'
        defaultMode: 420
    - name: vol-dir-0
      emptyDir: {}
    - name: vol-dir-1
      emptyDir: {}
    - name: vol-dir-2
      emptyDir: {}
    - name: vol-dir-3
      emptyDir: {}
    - name: vol-dir-4
      emptyDir: {}
    - name: vol-dir-5
      emptyDir: {}
    - name: vol-dir-6
      emptyDir: {}
    - name: vol-dir-7
      emptyDir: {}
    - name: vol-dir-8
      emptyDir: {}
