{{- $appName := .Values.appName }}
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: {{ printf "%s-mpp-agent-sidecarset" $appName | trunc 63 | trimSuffix "-" }}
spec:
  selector:
    matchLabels:
      mpp.aliyun.com/agent-sidecarset: "true"
      sigma.ali/app-name: "{{ $appName }}"
  updateStrategy:
    type: RollingUpdate
    paused: false
    maxUnavailable: 1
  containers:
    - name: agent
      image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
      {{- range .Values.transferEnvs }}
      transferEnv:
        - envName: "{{ .envName }}"
          sourceContainerName: "{{ .sourceContainerName }}"
      {{- end }}
      {{- range .Values.envs }}
      env:
        - name: {{ .name }}
          valueFrom:
            fieldRef:
              apiVersion: {{.apiVersion }}
              fieldPath: metadata.uid
      {{- end }}
      resources:
        limits:
          cpu: '1'
          memory: 1Gi
        requests:
          cpu: '1'
          memory: 1Gi
      {{- range .Values.volumeMounts }}
      volumeMounts:
        - name: "{{ .name }}"
          mountPath: "{{ .mountPath }}"
      {{- end }}
      imagePullPolicy: Always
  volumes: {{.Values.volumes | toYaml | nindent 4 }}


{{/*helm template .*/}}