# sidecarset.yaml
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: mpp-agent-sidecarset-video-trans
spec:
  selector:
    matchLabels:
      mpp.aliyun.com/agent-sidecarset: "true"
      sigma.ali/app-name: "video-trans"
  updateStrategy:
    type: RollingUpdate
    paused: false
    maxUnavailable: 1
  containers:
    - name: agent
      image: mpp-pre-registry-vpc.cn-shanghai.cr.aliyuncs.com/mpp/mpp-agent:174938105
#      image: mpp-pre-registry-vpc.cn-shanghai.cr.aliyuncs.com/mpp/mpp-agent:last-pre
      transferEnv:
        - envName: APPSTACK_APP_NAME
          sourceContainerName: main
        - envName: APPSTACK_ENV_LEVEL
          sourceContainerName: main
        - envName: APPSTACK_ENV_TAG
          sourceContainerName: main
        - envName: APPSTACK_REGION
          sourceContainerName: main
        - envName: APPSTACK_UNIT
          sourceContainerName: main
        - envName: APPSTACK_AZ
          sourceContainerName: main
        - envName: MPP_PRODUCT
          sourceContainerName: main
        - envName: MPP_MODELID
          sourceContainerName: main
        - envName: MPP_TAG
          sourceContainerName: main
        - envName: MPP_AGENT_START_MODE
          sourceContainerName: main
      env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: CSE_NODE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: CSE_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: CSE_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: CSE_POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: APP_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/app-name']
        - name: K8S_CONTAINER_NAME
          value: agent
      resources:
        limits:
          cpu: '1'
          memory: 1Gi
        requests:
          cpu: '1'
          memory: 1Gi
      volumeMounts:
        - name: plugins-dir
          mountPath: /home/<USER>/plugins
        - name: shared-tmp
          mountPath: /tmp
        - name: vol-dir-1
          mountPath: /home/<USER>/mpp-worker-agent/
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
  volumes:
    - name: shared-tmp
      emptyDir: {}
    - name: plugins-dir
      emptyDir: {}
    - name: cse-staragent-sn
      downwardAPI:
        items:
          - path: staragent_sn
            fieldRef:
              apiVersion: v1
              fieldPath: 'metadata.labels[''sigma.ali/sn'']'
        defaultMode: 420
    - name: vol-dir-0
      emptyDir:
        medium: Memory
        sizeLimit: 100Gi
    - name: vol-dir-1
      emptyDir: {}
    - name: anaconda3
      hostPath:
        path: /ai/anaconda3-7-30
        type: ''
    - name: models
      hostPath:
        path: /ai/models-vocals
        type: ''