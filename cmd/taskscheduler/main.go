package main

import (
	"context"
	"flag"
	"fmt"
	"mpp/pkg/metrics"
	"os"

	"github.com/cinience/animus/registry"
	"mpp/pkg/tracer"
	"mpp/pkg/utils"

	zapWrapper "github.com/cinience/animus/contribs/log/zap"

	"github.com/cinience/animus"
	"github.com/cinience/animus/config"
	_ "mpp/pkg/metrics"
	_ "mpp/pkg/tracer"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "github.com/cinience/animus/contribs/config/nacos"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"

	"mpp/internal/taskscheduler/conf"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	Name = "mpp-taskscheduler"
	flag.StringVar(&config.FlagConf, "conf", "./configs/taskscheduler", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, conf *conf.Data, gs *grpc.Server, hs *http.Server, rr []registry.Registrar) *animus.App {
	so := animus.WithDefaultHealthCheck()
	so = append(so,
		animus.ID(id),
		animus.Name(Name),
		animus.Version(Version),
		animus.Metadata(map[string]string{}),
		animus.Logger(logger),
		animus.Server(
			gs,
			hs,
		),
		animus.Registrar(rr),
	)
	return animus.New(so...)
}

func GetCustomLogger(name string) log.Logger {
	lumberjackLogger := &lumberjack.Logger{
		Filename:   fmt.Sprintf("logs/%s.log", name), // 日志文件路径
		MaxSize:    500,                              // 单个日志文件的最大大小（单位：MB）
		MaxBackups: 3,                                // 最多保留的旧日志文件数
		MaxAge:     7,                                // 保留的旧日志文件的最大天数
		Compress:   false,                            // 是否启用日志压缩
	}

	writeSyncer := zapcore.AddSync(lumberjackLogger)
	zapConfig := zap.NewProductionEncoderConfig()
	zapConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	zapConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoder := zapcore.NewJSONEncoder(zapConfig)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	z := zap.New(core)

	return zapWrapper.NewLogger(z)
}

func main() {
	flag.Parse()

	l := GetCustomLogger(Name)

	log.SetLogger(l)

	logger := log.With(l,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", RequestID(),
		"task.id", TaskID(),
	)

	var bc conf.Bootstrap

	if err := config.NewConfig(&bc); err != nil {
		panic(err)
	}

	fmt.Printf("conf is %+v \n", bc)
	//增加资源池tag
	if len(bc.App.TaskSchedulerTag) > 0 {
		Name = fmt.Sprintf("%s-%s", Name, bc.App.TaskSchedulerTag)
	}

	var err error

	_, meterProvider := metrics.SetupMetricsServer()
	err = tracer.SetupTracer(bc.Server.TracingUrl, "", Version, meterProvider)
	if err != nil {
		// 可以容忍的错误
		log.Warnf("setupTracer failed. err: %v", err)
	}

	app, cleanup, err := wireApp(bc.Server, bc.Registry, bc.LiveTransCodeConfig, bc.Data, logger, bc.Swagger, bc.App)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

func RequestID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return utils.GetRequestIdFromContext(ctx)
	}
}

func TaskID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return utils.GetTaskIdFromContext(ctx)
	}
}
