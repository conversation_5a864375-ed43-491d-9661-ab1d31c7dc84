/******************************************/
/*   DatabaseName = taskscheduler   */
/*   TableName = workers   */
/******************************************/
CREATE TABLE IF NOT EXISTS `workers` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `uuid` varchar(64) NOT NULL COMMENT '唯一ID',
    `ip` varchar(64) NOT NULL COMMENT 'IP地址',
    `port` int(11) COMMENT '端口',
    `product` varchar(32) NOT NULL COMMENT '产品',
    `model` varchar(32) NOT NULL COMMENT '模型',
    `tag` varchar(32) NOT NULL COMMENT '组别标志',
    `gmt_heartbeat` date NOT NULL COMMENT '心跳时间',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0-不在线；1-在线',
    `online_version` int(11) NOT NULL DEFAULT '0' COMMENT '每次上线加1',
    `name` varchar(64) DEFAULT NULL COMMENT '名称',
    `total_quota` varchar(1024) DEFAULT NULL COMMENT '多维度quota上限值',
    `real_quota` varchar(1024) DEFAULT NULL COMMENT '多维度quota实际值',
    `alloc_quota` varchar(1024) DEFAULT NULL COMMENT '多维度quot逻辑值',
    `avg_quota` varchar(1024) DEFAULT NULL COMMENT '平均Quota',
    `label` varchar(1024) DEFAULT NULL COMMENT '标签组',
    `taint` varchar(1024) DEFAULT NULL COMMENT '污点组',
    `load` decimal(10,2) DEFAULT '0.00' COMMENT 'worker负载',
    `gmt_latest_alloc` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '资源最后申请时间，用于资源申请冻结',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uuid` (`uuid`),
    KEY `idx_tag` (`tag`),
    KEY `idx_status_uuid` (`status`,`uuid`),
    KEY `idx_ip` (`ip`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='worker表';


show tables;

select * from strategies;
update strategies set config = '{"Dominants": ["cpu"], "LoadRatio": 50, "LimitRatio": {"cpu": 50}, "MaxTaskNum": 500, "ScheduleResourceType": "real"}' where id = 1;
select * from greeters;

show create table water_levels;


select TIMESTAMPDIFF(SECOND, gmt_create, now()) from workers;
select uuid, quota, alloc_quota, avg_quota, real_quota, schedule_quota, gmt_latest_alloc, gmt_heartbeat,gmt_modified from  workers;
select * from workers where status = 1;
select * from resources where status = 1;
delete from workers where worker_id = '12345678';
update  workers set schedule_quota = '{}' where id = 1;

UPDATE workers SET schedule_quota = JSON_SET(schedule_quota, '$.cpu', CAST( schedule_quota->>'$.cpu' AS SIGNED)+200) WHERE worker_id = 'aaaaaaaa' and schedule_quota->>'$.cpu' >= 200;


select * from resources;
select * from workers;
update workers set status = 0 where id = 1;
select * from water_levels order by id desc ;
select * from strategies;
update strategies set config = '{"scheduleResourceType":"Real","limitRatio":{"cpu":50},"maxTaskNum":500,"loadRatio":50,"dominants":["cpu"]}' where strategy_id = 'e217a0be96a6587a9be2ccda4fa52f05'

truncate workers;
truncate resources;
truncate strategies;
truncate water_levels;


select * from leaders;
update leaders set instance = '1.1.1.1' where id = 1;

desc water_levels
desc leaders;
desc workers;
alter table workers change uuid  worker_id varchar(64)
desc resources;
desc strategies;
drop table resources;
drop table workers;
drop table leaders;
drop table water_levels;

delete from resources;

SELECT VERSION();

SELECT * FROM `leaders` WHERE key = 'TaskSchedule' AND instance = '' AND TIMESTAMPDIFF(SECOND, gmt_tick, now()) <= 3 LIMIT 1

INSERT INTO leaders(gmt_create, gmt_modified, `key`, instance, version, gmt_tick)
VALUES(CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'TaskSchedule', '*************', 0, CURRENT_TIMESTAMP)

select * from workers;
delete from workers where uuid = 'worker-uuid-batch-0'
INSERT INTO workers(gmt_create, gmt_modified, uuid, pod_name, node_name, ip, port, product, engine_model, tag, gmt_heartbeat, status, online_version, quota, alloc_quota, avg_quota, real_quota, schedule_quota, gmt_latest_alloc, label, taint, `load`, extend)
VALUES(now(), now(), 'worker-uuid-batch-0', 'pod-name-batch-0', 'node-name-batch-0', '*******', 90, 'ols', 'transcode', 'livetranscode_pub', now(), 1, 0, '{"cpu":3200}', '{"cpu":200}', '{"cpu":130}', '{"cpu":100}', '{"cpu":3000}', now(), '{"app":"livetranscode_pub_0"}', '{"taint":"test_0"}', 2, '{"extend":"test_0"}')
ON DUPLICATE KEY UPDATE pod_name = 'pod-name-batch-0', node_name = 'node-name-batch-0', ip = '*******', port = 90, product = 'ols', engine_model = 'transcode', tag = 'livetranscode_pub', quota = '{"cpu":3200}', alloc_quota = '{"cpu":200}', avg_quota = '{"cpu":130}', real_quota = '{"cpu":100}', schedule_quota = '{"cpu":3000}', status = 1, label = '{"app":"livetranscode_pub_0"}', taint = '{"taint":"test_0"}', `load` = 2, extend = '{"extend":"test_0"}', online_version = online_version+1, gmt_heartbeat = now(), gmt_modified = now()


INSERT INTO workers(gmt_create, gmt_modified, uuid, pod_name, node_name, ip, port, product, engine_model, tag, gmt_heartbeat, status, online_version, update_quota_version, quota, alloc_quota, avg_quota, real_quota, schedule_quota, schedule_config, gmt_latest_alloc, label, taint, `load`, extend) VALUES(now(), now(), 'aaaaaaaa', '', '', '127.0.0.1', 8000, 'ols', 'livetranscode', 'livetranscode_pub', now(), 1, 0, 0, '{"cpu":3200}', 'null', 'null', 'null', 'null', '{}', now(), 'null', 'null', 0, 'null') ON DUPLICATE KEY UPDATE pod_name = '', node_name = '', ip = '127.0.0.1', port = 8000, product = 'ols', engine_model = 'livetranscode', tag = 'livetranscode_pub', quota = '{"cpu":3200}', alloc_quota = 'null', avg_quota = 'null', real_quota = 'null', schedule_quota = 'null', schedule_config = '{}', status = 1, update_quota_version = 0, label = 'null', taint = 'null', `load` = 0, extend = 'null', online_version = online_version+1, gmt_heartbeat = now(), gmt_modified = now()
