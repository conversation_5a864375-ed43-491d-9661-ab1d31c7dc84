// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/cinience/animus"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/server"
	"mpp/internal/taskscheduler/service"
)

import (
	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "github.com/cinience/animus/contribs/config/nacos"
	_ "go.uber.org/automaxprocs"
	_ "mpp/pkg/metrics"
	_ "mpp/pkg/tracer"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, registry *conf.Registry, liveTransCodeConfig *conf.LiveTransCodeConfig, confData *conf.Data, logger log.Logger, swagger *conf.Swagger, app *conf.App) (*animus.App, func(), error) {
	db, err := data.NewDB(confData)
	if err != nil {
		return nil, nil, err
	}
	dataData, cleanup, err := data.NewData(confData, logger, db)
	if err != nil {
		return nil, nil, err
	}
	workerRepo := data.NewWorkerRepo(dataData, logger)
	workerApi := data.NewWorkerApi(dataData, logger)
	taskScheduleMasterApi := data.NewTaskScheduleMasterClient(dataData, logger)
	workerManager := biz.NewWorkerManager(app, workerRepo, workerApi, taskScheduleMasterApi, logger)
	childWorkerRepo := data.NewChildWorkerRepo(dataData, logger)
	childWorkerManager := biz.NewChildWorkerManager(app, childWorkerRepo, taskScheduleMasterApi, logger)
	workerService := service.NewWorkerService(workerManager, childWorkerManager, logger)
	resourceRepo := data.NewResourceRepo(dataData, logger)
	discovery := server.NewDiscover(registry)
	taskManagerApi := data.NewTaskManagerApi(app, discovery, dataData, logger)
	resourceManager := biz.NewResourceManager(resourceRepo, taskManagerApi, logger)
	resourceService := service.NewResourceService(resourceManager, logger)
	leaderRepo := data.NewDBLeaderRepo(dataData, logger)
	scheduleManager := biz.NewScheduleManager(leaderRepo, logger)
	strategyRepo := data.NewStrategyRepo(dataData, logger)
	strategyManager := biz.NewStrategyManager(strategyRepo, logger)
	waterLevelRepo := data.NewWaterLevelRepo(dataData, logger)
	jobManagerClusterApi := data.NewJobManagerClusterApi(app, dataData, logger)
	waterLevelManager := biz.NewWaterLevelManager(waterLevelRepo, jobManagerClusterApi, logger)
	scheduleService := service.NewScheduleService(app, scheduleManager, resourceManager, workerManager, childWorkerManager, strategyManager, waterLevelManager, leaderRepo, taskScheduleMasterApi, logger)
	strategyService := service.NewStrategyService(strategyManager, logger)
	waterLevelService := service.NewWaterLevelService(waterLevelManager, workerManager, logger)
	grpcServer := server.NewGRPCServer(confServer, workerService, resourceService, scheduleService, strategyService, waterLevelService, logger, swagger)
	httpServer := server.NewHTTPServer(confServer, workerService, resourceService, scheduleService, strategyService, waterLevelService, logger, discovery, swagger)
	v := server.NewRegister(registry, liveTransCodeConfig)
	animusApp := newApp(logger, confData, grpcServer, httpServer, v)
	return animusApp, func() {
		cleanup()
	}, nil
}
