//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"github.com/cinience/animus"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/server"
	"mpp/internal/taskscheduler/service"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Registry, *conf.LiveTransCodeConfig, *conf.Data, log.Logger, *conf.Swagger, *conf.App) (*animus.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
