package main

import (
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"strconv"
	"syscall"

	"github.com/go-kratos/kratos/v2/errors"
)

const taskPidDir = "/home/<USER>/mpp-worker-agent/task-pids/"

var command = flag.String("command", "", "The shell command to execute")
var taskPid = flag.String("taskPid", "", "The task Pid File")
var mode = flag.String("mode", "", "The shell command to execute")

func main() {
	logFile, err := os.OpenFile("/home/<USER>/mpp-worker-agent/shellproxy.log", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0766)
	if err != nil {
		log.Fatalf("Error opening log file: %v", err)
		return
	}
	log.SetOutput(logFile) // 将文件设置为log输出的文件
	log.SetFlags(log.LstdFlags | log.Lshortfile | log.LUTC)

	log.Printf("Starting shellproxy All arguments: %v", os.Args)
	flag.Parse()
	if *mode == "" {
		log.Fatalf("Error: --mode is required. Usage: %s --mode=\"your-shell-command\"", flag.Arg(0))
		return
	}

	switch *mode {
	case "exec":
		ExecShellCommand(false)
	case "execRoot":
		ExecShellCommand(true)
	case "kill":
		ExecKillCommand(false)
	case "rootKill":
		ExecKillCommand(true)
	}
}

func ExecKillCommand(rootKill bool) {
	log.Printf("Starting shellproxy with taskPid: %s", *taskPid)
	if *taskPid == "" {
		log.Fatalf("Error: --taskPid is required. Usage: %s --taskPid=\"your-task-pid\"", flag.Arg(0))
	}
	pgid, err := strconv.Atoi(*taskPid)
	if err != nil {
		log.Fatalf("Error: --taskPid is required. Usage: %s --taskPid=\"your-task-pid\"", flag.Arg(0))
		return
	}
	if rootKill {
		err = syscall.Kill(-pgid, syscall.SIGKILL)
		if err != nil {
			log.Printf("Kill Command Failed Error: %v", err)
			return
		}
	} else {
		cmd := exec.Command("kill", "-9", fmt.Sprintf("%d", pgid))
		if err := cmd.Run(); err != nil {
			log.Printf("Kill Command Failed Error: %v", err)
			return
		}
	}
}

func ExecShellCommand(rootKill bool) {
	log.Printf("Starting shellproxy with command: %s", *command)

	if *command == "" || *taskPid == "" {
		log.Fatalf("Error: --command and -- taskId is required. Usage: %s --command=\"your-shell-command\"", flag.Arg(0))
		return
	}

	// Delete the tasker result file if it exists
	if err := DeleteFileIfExists(taskPidDir, *taskPid); err != nil {
		log.Fatalf("Error Delete the task pid result: %v", err)
		return
	}
	err := os.MkdirAll(taskPidDir, 0755)
	if err != nil {
		log.Fatalf("Error creating task pid dir: %v", err)
		return
	}
	// 打开文件，如果不存在则创建
	file, err := os.OpenFile(taskPidDir+*taskPid, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// 1. 创建命令
	cmd := exec.Command("sh", "-c", *command)
	if rootKill {
		cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
	}

	// 2. 获取标准输出和标准错误管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		log.Fatalf("Error creating StdoutPipe: %v", err)
		return
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		log.Fatalf("Error creating StderrPipe: %v", err)
		return
	}

	// 3. 启动命令
	if err := cmd.Start(); err != nil {
		log.Fatalf("Error starting command: %v", err)
		return
	}

	// 写入内容
	content := cmd.Process.Pid
	_, err = file.WriteString(strconv.Itoa(content))
	if err != nil {
		log.Fatalf("Error writing  pid to file:%v", err)
		return
	}

	// 4. 读取标准输出和标准错误
	outBytes, err := io.ReadAll(stdout)
	if err != nil {
		log.Fatalf("Warning: Error reading stdout: %v", err)
		return
	}
	errBytes, err := io.ReadAll(stderr)
	if err != nil {
		log.Fatalf("Warning: Error reading stderr: %v", err)
		return
	}

	// 5. 等待命令完成
	if err := cmd.Wait(); err != nil {
		var exitErr *exec.ExitError
		if errors.As(err, &exitErr) {
			fmt.Printf("command execute failed: output %v,err %v",
				string(outBytes)+string(errBytes),
				exitErr)
			os.Exit(exitErr.ExitCode())
		} else {
			// 其他类型错误
			fmt.Printf("command failed with unexpected error: %v", err)
			os.Exit(1)
		}
	}

	// 删除任务pid文件
	if err = DeleteFileIfExists(taskPidDir, *taskPid); err != nil {
		log.Printf("Error Delete the task pid result: %v", err)
	}

	// 打印命令输出
	fmt.Print(string(outBytes) + string(errBytes))
}

func DeleteFileIfExists(dir string, name string) error {
	file := dir + name
	if _, err := os.Stat(file); !os.IsNotExist(err) {
		err := os.Remove(file)
		if err != nil {
			return err
		}
	}

	return nil
}
