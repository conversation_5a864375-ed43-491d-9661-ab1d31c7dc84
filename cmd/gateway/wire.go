//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"context"
	"github.com/cinience/animus"
	"mpp/internal/gateway/pkg/conf"
	"mpp/internal/gateway/pkg/server"
	"mpp/internal/gateway/pkg/service"

	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(context.Context, *conf.Config) (*animus.App, func(), error) {
	panic(wire.Build(server.ProviderSet, service.ProviderSet))
}
