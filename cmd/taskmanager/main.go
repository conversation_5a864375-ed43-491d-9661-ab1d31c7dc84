package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"os"
	"strings"

	"github.com/cinience/animus"
	"github.com/cinience/animus/config"
	"github.com/cinience/animus/registry"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/hostinfo"
	"mpp/pkg/metrics"
	_ "mpp/pkg/metrics"
	"mpp/pkg/tracer"
	_ "mpp/pkg/tracer"
	"mpp/pkg/utils"

	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "github.com/cinience/animus/contribs/config/nacos"
	zapWrapper "github.com/cinience/animus/contribs/log/zap"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/natefinch/lumberjack"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"mpp/internal/taskmanager/conf"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string

	id, _ = os.Hostname()
)

func init() {
	Name = "mpp-taskmanager"
	flag.StringVar(&config.FlagConf, "conf", "./configs/taskmanager/", "config path, eg: -conf config.yaml")
}

func newApp(ctx context.Context, logger log.Logger, gs *grpc.Server, hs *http.Server, rr []registry.Registrar) *animus.App {
	so := animus.WithDefaultHealthCheck()
	so = append(so,
		animus.ID(id),
		animus.Name(Name),
		animus.Version(Version),
		animus.Metadata(map[string]string{}),
		animus.Logger(logger),
		animus.Server(
			gs,
			hs,
		),
		animus.Registrar(rr),
	)
	return animus.New(so...)
}

func GetCustomLogger(name string) log.Logger {
	lumberjackLogger := &lumberjack.Logger{
		Filename:   fmt.Sprintf("logs/%s.log", name), // 日志文件路径
		MaxSize:    500,                              // 单个日志文件的最大大小（单位：MB）
		MaxBackups: 3,                                // 最多保留的旧日志文件数
		MaxAge:     7,                                // 保留的旧日志文件的最大天数
		Compress:   false,                            // 是否启用日志压缩
	}

	writeSyncer := zapcore.AddSync(lumberjackLogger)

	zapConfig := zap.NewProductionEncoderConfig()
	zapConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	zapConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoder := zapcore.NewJSONEncoder(zapConfig)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	z := zap.New(core)

	return zapWrapper.NewLogger(z)
}

func main() {
	flag.Parse()

	l := GetCustomLogger(Name)

	log.SetLogger(l)

	logger := log.With(l,
		"trace.id", tracing.TraceID(),
		"request.id", RequestID(),
		"task.id", TaskID(),
		"task.product", TaskProduct(),
	)

	ctx, cancel := context.WithCancel(context.Background())

	var bc conf.Bootstrap
	if err := config.NewConfig(&bc); err != nil {
		panic(err)
	}
	// 设置集群信息
	models.SetClusterId(bc.Cluster)
	var err error

	promReg, meterProvider := metrics.SetupMetricsServer()
	err = tracer.SetupTracer(bc.Server.TracingUrl, "", Version, meterProvider)
	if err != nil {
		// 可以容忍的错误
		log.Warnf("setupTracer failed. err: %v", err)
	}

	if bc.Notifier == nil {
		bc.Notifier = &conf.Notifier{}
	}
	bc.Notifier.Address, err = getNotifierAddr(bc.Server)
	if err != nil {
		panic(err)
	}

	app, cleanup, err := wireApp(ctx, &bc, bc.Server, bc.Data, bc.Registry, bc.Notifier, logger, promReg, bc.Swagger, bc.LiveTransCodeConfig)
	if err != nil {
		panic(err)
	}
	defer func() {
		fmt.Println("enter cleanup defer...")
		cancel()
		cleanup()
	}()
	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}

}

func getNotifierAddr(server *conf.Server) (string, error) {
	httpAddr := server.Http.Addr
	if strings.HasPrefix(httpAddr, ":") {
		httpAddr = fmt.Sprintf("%s%s", hostinfo.GetHostIP(), httpAddr)
	}
	host, portStr, err := net.SplitHostPort(httpAddr)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("http://%s:%s", host, portStr), nil
}

func RequestID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return utils.GetRequestIdFromContext(ctx)
	}
}

func TaskID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return utils.GetTaskIdFromContext(ctx)
	}
}

func TaskProduct() log.Valuer {
	return func(ctx context.Context) interface{} {
		return utils.GetTaskProductFromContext(ctx)
	}
}
