//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"context"

	"github.com/cinience/animus"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/prometheus/client_golang/prometheus"

	"mpp/internal/taskmanager/biz"
	"mpp/internal/taskmanager/bouncer"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/infra"
	"mpp/internal/taskmanager/mesh"
	"mpp/internal/taskmanager/qproxy"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/repository/impl"
	"mpp/internal/taskmanager/server"
	"mpp/internal/taskmanager/service"
)

// wireApp init kratos application.
func wireApp(context.Context, *conf.Bootstrap, *conf.Server, *conf.Data, *conf.Registry, *conf.Notifier, log.Logger, prometheus.Registerer, *conf.Swagger, *conf.LiveTransCodeConfig) (*animus.App, func(), error) {
	panic(wire.Build(server.ProviderSet, repository.ProviderSet, mesh.ProviderSet, biz.ProviderSet, service.ProviderSet, infra.ProviderSet, qproxy.ProviderSet, impl.ProviderSet, bouncer.ProviderSet,
		newApp))
}
