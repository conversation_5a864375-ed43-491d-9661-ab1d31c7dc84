// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"github.com/cinience/animus"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/prometheus/client_golang/prometheus"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/dag"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/pipeline"
	"mpp/internal/taskmanager/biz/quota"
	"mpp/internal/taskmanager/biz/router"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/bouncer/balance"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/infra/engine"
	"mpp/internal/taskmanager/infra/scheduler"
	"mpp/internal/taskmanager/infra/workflow"
	"mpp/internal/taskmanager/mesh"
	"mpp/internal/taskmanager/qproxy/client"
	server2 "mpp/internal/taskmanager/qproxy/server"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/repository/impl"
	"mpp/internal/taskmanager/server"
	"mpp/internal/taskmanager/service"
)

import (
	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "github.com/cinience/animus/contribs/config/nacos"
	_ "go.uber.org/automaxprocs"
	_ "mpp/pkg/metrics"
	_ "mpp/pkg/tracer"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(contextContext context.Context, bootstrap *conf.Bootstrap, confServer *conf.Server, data *conf.Data, registry *conf.Registry, notifier *conf.Notifier, logger log.Logger, registerer prometheus.Registerer, swagger *conf.Swagger, liveTransCodeConfig *conf.LiveTransCodeConfig) (*animus.App, func(), error) {
	db, err := repository.NewDB(data, liveTransCodeConfig)
	if err != nil {
		return nil, nil, err
	}
	backUpDatabase, err := repository.NewDbBackup(data, liveTransCodeConfig)
	if err != nil {
		return nil, nil, err
	}
	taskRepo := impl.NewTaskRepo(repositoryData, logger)
	sourceStreamTaskRepo := impl.NewDBSourceStreamTaskStore(repositoryData, logger)
	discovery := server.NewDiscovery(registry)
	scheduleAdapter, cleanup2, err := scheduler.NewScheduleAdapter(contextContext, discovery)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	kvStoreRepo := impl.NewDBKVStore(repositoryData, logger)
	globalParamLoader := global_param_loader.NewGlobalParamLoader(logger, kvStoreRepo, bootstrap)
	engineAdapter := engine.NewAdapter(notifier, logger, globalParamLoader)
	mppTaskApi := workflow.NewMppClient(discovery, logger)
	workflowAdapter, cleanup3, err := workflow.NewWorkflowAdapter(bootstrap, discovery, mppTaskApi, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	redisClient := repository.NewRedisClient(bootstrap)
	meshService := mesh.NewMeshService(redisClient)
	taskSentinelConfigRepo := impl.NewTaskSentinelConfigRepoRepo(repositoryData, logger)
	sentinelBiz := sentinel.NewSentinelBiz(bootstrap, taskSentinelConfigRepo, logger, redisClient)
	liveTransCodeParamsAdapter := livetranscode.NewLiveTransCodeParamsAdapter(globalParamLoader, logger)
	transcodeAttachmentRepo := impl.NewDBTranscodeAttachment(repositoryData, logger)
	transcodeStatInfoRepo := impl.NewDBTranscodeStatInfo(repositoryData, logger)
	commonTaskBiz, err := tasker.NewTaskBiz(bootstrap, taskRepo, sourceStreamTaskRepo, scheduleAdapter, engineAdapter, workflowAdapter, meshService, sentinelBiz, liveTransCodeParamsAdapter, transcodeAttachmentRepo, transcodeStatInfoRepo, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	treeBalance := balance.NewTreeBalance()
	leaderRepo := impl.NewLeaderRepo(repositoryData, logger)
	leaderBiz := leader.NewLeaderBiz(leaderRepo, logger)
	mixGrayConfigRepo := impl.NewMixGrayConfigRepo(repositoryData, logger)
	mixRuleConfigBiz, err := grayscale.NewMixRuleConfigBiz(logger, bootstrap, mixGrayConfigRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	asyncQueueClient := client.NewAsyncQueueClient(bootstrap)
	taskDependency := dag.NewTaskDependency(redisClient, logger)
	batchTaskBiz, err := tasker.NewBatchTaskBiz(bootstrap, taskRepo, logger, scheduleAdapter, engineAdapter, workflowAdapter, meshService, treeBalance, leaderBiz, sentinelBiz, mixRuleConfigBiz, asyncQueueClient, taskDependency)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	recordHlsTaskBiz, err := tasker.NewRecordHlsTaskBiz(bootstrap, taskRepo, logger, engineAdapter, scheduleAdapter, workflowAdapter, meshService, sentinelBiz)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	simpleTaskBiz, err := tasker.NewSimpleTaskBiz(logger, engineAdapter, scheduleAdapter, mixRuleConfigBiz)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	taskStatusSync, err := tasker.NewTaskStatusSync(contextContext, bootstrap, taskRepo, engineAdapter, commonTaskBiz, leaderBiz, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	engineTaskSync := tasker.NewEngineTaskSync(contextContext, taskRepo, commonTaskBiz, leaderBiz, scheduleAdapter, engineAdapter, logger)
	schedulerResourceSync := tasker.NewSchedulerResourceSync(contextContext, taskRepo, commonTaskBiz, leaderBiz, scheduleAdapter, logger)
	asynqServer, cleanup4, err := server2.NewAsynqServer(bootstrap, registerer)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	routerRouter := router.NewRouter(bootstrap, redisClient)
	taskSentinelSync := tasker.NewTaskSentinelSync(contextContext, taskRepo, taskSentinelConfigRepo, commonTaskBiz, batchTaskBiz, leaderBiz, sentinelBiz, logger)
	quotaService := quota.NewQuotaService(bootstrap)
	batchTaskSync, err := tasker.NewBatchTaskSync(contextContext, bootstrap, taskRepo, engineAdapter, commonTaskBiz, batchTaskBiz, leaderBiz, scheduleAdapter, sentinelBiz, workflowAdapter, meshService, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	switchConfigRepo := impl.NewSwitchConfigRepo(repositoryData, logger)
	switchService := common.NewSwitchService(switchConfigRepo, logger)
	schedulerSwitchOperator := livetranscode.NewSchedulerSwitchOperator(logger, switchService)
	unitTaskRepo := impl.NewUnitTaskRepo(repositoryData, logger)
	haService := ha.NewHAService(logger, taskRepo, unitTaskRepo, bootstrap, globalParamLoader)
	taskService, err := service.NewTaskService(bootstrap, commonTaskBiz, batchTaskBiz, recordHlsTaskBiz, simpleTaskBiz, taskStatusSync, engineTaskSync, schedulerResourceSync, asynqServer, logger, routerRouter, taskSentinelSync, quotaService, batchTaskSync, globalParamLoader, schedulerSwitchOperator, liveTransCodeParamsAdapter, haService)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	asyncTaskPipelineRepo := impl.NewAsyncTaskPipelineRepo(repositoryData, logger)
	commonPipelineBiz := pipeline.NewCommonPipelineBiz(asyncTaskPipelineRepo, taskSentinelConfigRepo, logger)
	pipelineService := service.NewPipelineService(bootstrap, commonPipelineBiz, sentinelBiz, logger)
	grayscaleService, err := service.NewGrayscaleService(logger, mixRuleConfigBiz)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	grpcServer := server.NewGRPCServer(confServer, taskService, pipelineService, meshService, grayscaleService, logger)
	unitTaskService := service.NewUnitTaskService(unitTaskRepo, logger, haService, globalParamLoader)
	grayConfigService := service.NewGrayConfigService(switchConfigRepo, logger)
	httpServer := server.NewHTTPServer(confServer, taskService, unitTaskService, pipelineService, meshService, grayscaleService, logger, discovery, asynqServer, registerer, swagger, grayConfigService)
	v := server.NewRegister(registry)
	app := newApp(contextContext, logger, grpcServer, httpServer, v)
	return app, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
