package main

import (
	"flag"
	"log"
	"net"
	"os"
	"strings"

	"github.com/sevlyar/go-daemon"
	"inet.af/tcpproxy"
)

func GetHostIP() string {
	// 支持从ResolveIPAddr获取本机IP
	var hostIP string
	hostName, err := os.Hostname()
	if err == nil {
		ipaddr, err := net.ResolveIPAddr("ip", hostName)
		if err == nil {
			hostIP = ipaddr.IP.String()
		}
	}
	return hostIP
}

var gatewayIP = flag.String("gateway", "", "gateway ip")

func main() {
	flag.Parse()

	ctx := &daemon.Context{
		PidFileName: "tiny_proxy.pid",
		PidFilePerm: 0644,
		LogFileName: "tiny_proxy.log",
		LogFilePerm: 0640,
		WorkDir:     "./",
		Umask:       027,
		Args:        os.Args,
	}

	d, err := ctx.Reborn()
	if err != nil {
		log.Fatal("Unable to run: ", err)
	}
	if d != nil {
		return
	}
	defer ctx.Release()

	log.Print("- - - - - - - - - - - - - - -")
	log.Print("daemon started")

	var tcpProxy tcpproxy.Proxy
	var proxyIp string
	if *gatewayIP == "" {
		ip := GetHostIP()
		if ip == "" {
			log.Println("can not get host ip")
			os.Exit(1)
		}
		ipArray := strings.Split(ip, ".")
		ipArray[3] = "1"
		proxyIp = strings.Join(ipArray, ".")
	} else {
		proxyIp = *gatewayIP
	}

	log.Println("tcp proxy to", proxyIp+":10881")
	log.Println("tcp proxy to", proxyIp+":10080")
	log.Println("tcp proxy to", proxyIp+":10882")
	tcpProxy.AddRoute(":10881", tcpproxy.To(proxyIp+":10881")) // fallback
	tcpProxy.AddRoute(":10080", tcpproxy.To(proxyIp+":10080"))
	tcpProxy.AddRoute(":10882", tcpproxy.To(proxyIp+":10882"))
	err = tcpProxy.Run()
	if err != nil {
		panic(err)
	}
}
