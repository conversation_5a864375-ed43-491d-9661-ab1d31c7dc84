// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"github.com/cinience/animus"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/biz"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/data"
	"mpp/internal/sensing/server"
	"mpp/internal/sensing/service"
	"mpp/internal/sensing/util"
)

import (
	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(contextContext context.Context, confServer *conf.Server, confData *conf.Data, business *conf.Business, logger log.Logger) (*animus.App, func(), error) {
	mnsClient := util.NewMnsClient(confData, logger)
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	eventRepo := data.NewEventRepo(dataData, logger)
	redisRepo := data.NewRedisRepo(dataData, logger)
	eventBiz := biz.NewEventBiz(confData, business, mnsClient, eventRepo, redisRepo, logger)
	eventService := service.NewEventService(eventBiz)
	robotBiz := biz.NewRobotBiz(confData)
	robotService := service.NewRobotService(robotBiz)
	grpcServer := server.NewGRPCServer(confServer, eventService, robotService, logger)
	httpServer := server.NewHTTPServer(confServer, eventService, robotService, logger)
	app := newApp(contextContext, logger, grpcServer, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
