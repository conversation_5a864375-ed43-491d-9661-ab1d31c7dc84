//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"context"
	"github.com/cinience/animus"
	"mpp/internal/sensing/biz"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/data"
	"mpp/internal/sensing/server"
	"mpp/internal/sensing/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(context.Context, *conf.Server, *conf.Data, *conf.Business, log.Logger) (*animus.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
