package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"github.com/cinience/animus"
	"github.com/cinience/animus/config"
	_ "github.com/cinience/animus/contribs/config/diamond"
	"mpp/internal/sensing/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/natefinch/lumberjack"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	Name = "mpp-sensing"
	flag.StringVar(&config.FlagConf, "conf", "./configs/sensing/", "config path, eg: -conf config.yaml")
}

func newApp(ctx context.Context, logger log.Logger, gs *grpc.Server, hs *http.Server) *animus.App {
	so := animus.WithDefaultHealthCheck()
	so = append(so,
		animus.ID(id),
		animus.Name(Name),
		animus.Version(Version),
		animus.Metadata(map[string]string{}),
		animus.Logger(logger),
		animus.Server(
			gs,
			hs,
		),
		//animus.Registrar(rr),
	)
	return animus.New(so...)
}

func main() {
	flag.Parse()
	// 创建一个 lumberjack.Logger 实例，用于日志滚动
	lumberjackLogger := &lumberjack.Logger{
		Filename:   fmt.Sprintf("log/%v.log", Name), // 日志文件路径
		MaxSize:    100,                             // 单个日志文件的最大大小（单位：MB）
		MaxBackups: 10,                              // 最多保留的旧日志文件数
		MaxAge:     30,                              // 保留的旧日志文件的最大天数
		Compress:   false,                           // 是否启用日志压缩
	}

	l := log.NewStdLogger(lumberjackLogger)
	log.SetLogger(l)

	logger := log.With(l,
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", RequestID(),
		"task.id", TaskID(),
	)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var bc conf.Bootstrap
	if err := config.NewConfig(&bc); err != nil {
		panic(err)
	}

	fmt.Printf("conf is %+v \n", bc)

	app, cleanup, err := wireApp(ctx, bc.Server, bc.Data, bc.Business, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
func RequestID() log.Valuer {
	return func(ctx context.Context) interface{} {
		value := ctx.Value("requestId")
		if value == nil {
			return ""
		} else {
			return value
		}
	}
}

func TaskID() log.Valuer {
	return func(ctx context.Context) interface{} {
		value := ctx.Value("taskId")
		if value == nil {
			return ""
		} else {
			return value
		}
	}
}
