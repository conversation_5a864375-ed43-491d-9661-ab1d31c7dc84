
export SQL_HOST=rm-uf6vfjzq2ch8a8bo9.mysql.rds.aliyuncs.com
export port=3306
export user=mpp_tc
export password=mpp_TEST
export dbname=mpp_workflow_live_sh_pre
export dbvisibility=mpp_workflow_visibility_live_sh_pre


## 创建数据库
```
./workflow-sql --ep $SQL_HOST -p $port --user $user --password $password --db $dbname --plugin mysql8 create
./temporal-sql --ep $SQL_HOST -p $port --user $user --password $password --db $dbvisibility --plugin mysql8 create
```

## 初始化表格
```
./workflow-sql --ep $SQL_HOST -p $port --user $user --password $password --plugin mysql8 --db $dbname setup-schema -v 0.0
./workflow-sql --ep $SQL_HOST -p $port --user $user --password $password --plugin mysql8 --db $dbname update-schema -d ./conf/workflow/mysql/v8/temporal/versioned

./workflow-sql --ep $SQL_HOST -p $port --user $user --password $password --plugin mysql8 --db $dbvisibility setup-schema -v 0.0
./workflow-sql --ep $SQL_HOST -p $port --user $user --password $password --plugin mysql8 --db $dbvisibility update-schema -d ./conf/workflow/mysql/v8/visibility/versioned
```
