package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	stdlog "log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"syscall"
	"time"

	"mpp/internal/agent/conf"
	"mpp/internal/agent/controller"
	"mpp/internal/agent/logs"
	"mpp/internal/agent/pkg/base"
	"mpp/internal/agent/pkg/utils"
	"mpp/internal/agent/service/mecha/engine"
	"mpp/internal/agent/types"
	"mpp/pkg/metrics"
	"mpp/pkg/tracer"
	"mpp/pkg/tracer/tasker_tracing"
	mppUtils "mpp/pkg/utils"

	nacos2 "mpp/internal/agent/pkg/nacos"
	"mpp/internal/agent/server"
	"mpp/internal/agent/service"

	zapWrapper "github.com/cinience/animus/contribs/log/zap"
	_ "github.com/cinience/animus/contribs/registry/nacos"
	"github.com/cinience/animus/registry"

	"github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"inet.af/tcpproxy"

	_ "mpp/pkg/tracer"
)

var (
	startTime = time.Now()
	Version   string
	BuildDate string
)

func checkErrorAndExit(err error) {
	if err != nil && !errors.Is(err, http.ErrServerClosed) {
		if strings.Contains(err.Error(), "use of closed network connection") {
			return
		}
		stdlog.Print(err)
		logs.Error("checkErrorAndExit ", err)
		panic(err)
	}
}

func GetCustomLogger(name string) log.Logger {
	lumberjackLogger := &lumberjack.Logger{
		Filename:   fmt.Sprintf("logs/%s.log", name), // 日志文件路径
		MaxSize:    256,                              // 单个日志文件的最大大小（单位：MB）
		MaxBackups: 3,                                // 最多保留的旧日志文件数
		MaxAge:     3,                                // 保留的旧日志文件的最大天数
		Compress:   false,                            // 是否启用日志压缩
	}

	writeSyncer := zapcore.AddSync(lumberjackLogger)
	zapConfig := zap.NewProductionEncoderConfig()
	zapConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	zapConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoder := zapcore.NewJSONEncoder(zapConfig)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	z := zap.New(core)

	return zapWrapper.NewLogger(z)
}

func main() {
	var confFile string
	var updateConfig bool

	flag.StringVar(&confFile, "config", "./conf/app.conf", "config file")
	flag.BoolVar(&updateConfig, "updateConfig", false, "update config file")

	flag.Parse()
	stdlog.Println("config file:", confFile)
	base.CommitID = Version
	base.BuildDate = BuildDate

	// 提升启动速度：启动时，GetAgentMetadata
	// 这里原本也没有生效使用，ret value not used.
	// base.GetAgentMetadata()

	conf.DefaultConfig.Init(confFile, updateConfig)

	stdlog.Println(conf.DefaultConfig.String())

	if updateConfig {
		os.Exit(0)
	}

	appName := "mpp-agent"
	l := GetCustomLogger(appName)
	log.SetLogger(log.With(l, "caller", Caller(5)))

	logger := log.With(l,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", RequestID(),
		"task.id", TaskID(),
	)

	// init loggers
	utils.InitTraceLogger()
	utils.InitTaskLogger()
	utils.InitCostLogger()
	utils.InitMonitorLogger()
	utils.InitRunTimeCostLogger()

	defer func() {
		// log panic and stack trace
		// Aone:
		//	https://project.aone.alibaba-inc.com/v2/project/1143023/bug/65324300#
		//	https://project.aone.alibaba-inc.com/v2/project/1143023/bug/61925512#
		if r := recover(); r != nil {
			// Info Log
			logs.Errorf("mpp-agent panic: %v, stack: %v", r, string(debug.Stack()))
			stdlog.Printf("mpp-agent panic: %v, stack: %v", r, string(debug.Stack()))
			// Trace Log
			utils.TraceLog.LogServiceEvent(utils.LOG_EVENT_SERVICE_PANIC, 0, utils.LL_ERROR, fmt.Sprintf("%v", r))
		}
	}()

	_, meterProvider := metrics.SetupMetricsServer()
	_ = tracer.SetupTracer("", appName, Version, meterProvider)

	engineOnceInstance := engine.GetKubeletMechaEngine()
	c := make(chan os.Signal, 1)
	taskServer, err := server.NewTaskServer(logger, engineOnceInstance)
	checkErrorAndExit(err)
	var tcpProxy tcpproxy.Proxy
	go func() {
		// 和引擎内部交互的接口，改为转发到主服务，便于记录日志
		localAddr := "localhost" + base.GetMainServerAddress()
		logs.Info("start proxy server. tcp proxy to %s", localAddr)
		tcpProxy.AddRoute(base.GetInnerServerAddress(), tcpproxy.To(localAddr)) // fallback
		terr := tcpProxy.Run()
		checkErrorAndExit(terr)
	}()

	invokeServer := controller.NewInvokeServer()
	// 同步任务服务
	go func() {
		terr := invokeServer.Run()
		checkErrorAndExit(terr)
	}()

	logs.Info("start agent http server...")
	go func() {
		terr := taskServer.Run()
		checkErrorAndExit(terr)
	}()
	if conf.IsDatahubOn() {
		flag.StringVar(&config.FlagConf, "conf", "./configs/agent/", "config path, eg: -conf config.yaml")
		flag.Parse()
		appName = "mpp-agent-datahub"
		l := GetCustomLogger(appName)
		log.SetLogger(log.With(l, "caller", Caller(5)))

		datahubLogger := log.With(l,
			"trace.id", tracing.TraceID(),
			"span.id", tracing.SpanID(),
			"request.id", RequestID(),
			"task.id", TaskID(),
		)
		dataHubServer, err := server.NewDataHubServer(datahubLogger, conf.GetDatahubConfig())
		checkErrorAndExit(err)
		go func() {
			terr := dataHubServer.Run()
			checkErrorAndExit(terr)
		}()
	}

	if conf.IsTestMode() {
		go initModules(engineOnceInstance)
	} else {
		initModules(engineOnceInstance)
	}

	defer finalizeModules()

	// Nacos init
	if conf.DefaultConfig.Schedule.NacosSwitchIsOn {
		if err := nacos2.DiscoveryInit(); err != nil {
			logs.Error("Nacos service discovery init failed. err:%v", err)
			logs.Debug("NacosAddr = %s, NacosPort = %d, NacosNameSpaceId = %s",
				conf.DefaultConfig.Schedule.NacosAddr, conf.DefaultConfig.Schedule.NacosPort, conf.DefaultConfig.Schedule.NacosNamespaceId)
			err = fmt.Errorf("nacos service discovery init failed. err:%v", err)
			checkErrorAndExit(err)
		}
		logs.Debug("NacosAddr = %s, NacosPort = %d, NacosNameSpaceId = %s",
			conf.DefaultConfig.Schedule.NacosAddr, conf.DefaultConfig.Schedule.NacosPort, conf.DefaultConfig.Schedule.NacosNamespaceId)
	}

	watchSignals(c)
	_ = tcpProxy.Close()

	logs.Info("stop agent http server...")
	err = taskServer.Stop()
	checkErrorAndExit(err)

	invokeServer.Stop()
}

func initModules(engineOnceInstance func() *engine.KubeletMechaEngine) {
	logs.Infof("init taskService start ...")
	stdlog.Print("init taskService start ...")
	startTime := time.Now()
	dis, err := registry.GetDiscoveryProvider(mppUtils.PatchRegistryUrl(conf.GetNacosAddr()))
	if err != nil {
		logs.Error("panic: GetDiscoveryProvider failed. err:%v", err)
		stdlog.Printf("GetDiscoveryProvider failed, agent will panic, err:%v", err)
		panic(err)
	}
	if dis == nil {
		err = service.TS.Init(nil, engineOnceInstance)
	} else {
		err = service.TS.Init(dis, engineOnceInstance)
	}
	if err != nil {
		logs.Error("panic: init taskService failed. err:%v", err)
		stdlog.Printf("init taskService failed, agent will panic, err:%v", err)
		panic(err)
	}

	delayTime := time.Now().Sub(startTime).Milliseconds()
	utils.TraceLog.LogServiceEvent(utils.LOG_EVENT_SERVICE_START, delayTime, utils.LL_INFO, types.RESULT_SUCCESS.Code)
	logs.Infof("init taskService success, cost time: %d[ms] ...", delayTime)
	stdlog.Printf("init taskService success, cost time: %d[ms] ...", delayTime)

	startTime = time.Now()
	service.TCS.Start()
	logs.Infof("init TaskQuotaCollectService success, cost time:%d[ms] ...", time.Now().Sub(startTime).Milliseconds())
	stdlog.Printf("init TaskQuotaCollectService success, cost time:%d[ms] ...", time.Now().Sub(startTime).Milliseconds())
}

func finalizeModules() {
	_ = service.TS.Shutdown()
}

// sigChildHandler
func sigChildHandler() {
	var sigs = make(chan os.Signal, 3)
	signal.Notify(sigs, syscall.SIGCHLD)
	for {
		var sig = <-sigs
		logs.Info("sigChildHandler: %v", sig)
		//select {
		//case <-sig: /*  published it.  */
		//default:
		//	/*
		//	 *  Notifications channel full - drop it to the
		//	 *  floor. This ensures we don't fill up the SIGCHLD
		//	 *  queue. The reaper just waits for any child
		//	 *  process (pid=-1), so we ain't loosing it!! ;^)
		//	 */
		//}
	}

}

func watchSignals(c chan os.Signal) {
	//  检讨子进程信号
	//go sigChildHandler()
	// listen interrupt & quit
	// syscall.SIGINT
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM, syscall.SIGUSR1)
	// ignore hangup signal
	signal.Ignore(syscall.SIGHUP)

	s := <-c
	signalName := s.String()
	logs.Info("receive signal:%v. wait task stop...", signalName)
	service.WS.SetAgentTerminating()
	tr := tasker_tracing.NewTracer(tasker_tracing.WithTracerName("mppAgent"))
	logs.Info("SetAgentTerminating")
	exitCtx := context.Background()
	utils.TraceLog.LogServiceEvent(utils.LOG_EVENT_SERVICE_INACTIVE, 0, utils.LL_WARN, types.RESULT_SUCCESS.Code)
	time.Sleep(time.Second * 3)

	start := time.Now()
	exitCtx, span := tr.Start(context.Background(), "/agent/exit/app", nil)
	defer tr.End(exitCtx, span, nil, nil)
	for {
		needExit := false
		exitReason := ""
		if s == syscall.SIGUSR1 { // kill -10
			utils.TraceLog.LogServiceEvent(utils.LOG_EVENT_SERVICE_USER_SIGNAL, 0, utils.LL_INFO, types.RESULT_SUCCESS.Code)
			exitReason = "signal.SIGUSR1"
			needExit = true
		} else if !conf.DefaultConfig.App.ExitWaitTask {
			detail := getRunningTaskInfo()
			utils.TraceLog.LogServiceEventWithDetailParam(utils.LOG_EVENT_SERVICE_NO_WAIT_EXIT, 0, utils.LL_INFO, types.RESULT_SUCCESS.Code,
				detail)
			logs.Info("Configure to not wait for tasks.")
			exitReason = "DoNotExitWaitTaskComplete"
			needExit = true
		} else if conf.DefaultConfig.App.ExitWaitSecond > 0 && time.Now().Unix()-service.TS.LastExitWaitStartTime < int64(conf.DefaultConfig.App.ExitWaitSecond) {
			// 异常退出等待分析
			time.Sleep(time.Second)
			logs.Info("Task get ExitWaitSecond, remain time:%v", int64(conf.DefaultConfig.App.ExitWaitSecond)-time.Now().Unix()+service.TS.LastExitWaitStartTime)
		} else if service.TS.TaskNum() == 0 {
			logs.Info("All task has already exited.")
			exitReason = "AllTaskComplete"
			needExit = true
		} else if !service.WS.IsEngineHealth && service.WS.UnHealthCount > 3 {
			detail := getRunningTaskInfo()
			utils.TraceLog.LogServiceEventWithDetailParam(utils.LOG_EVENT_SERVICE_UNHEALTH_EXIT, 0, utils.LL_INFO, types.RESULT_SUCCESS.Code, detail)
			logs.Info("Engine health is false.")
			exitReason = "EngineNotHealth"
			needExit = true
		} else {
			time.Sleep(time.Second)
			logs.Info("receive signal:%v. task num:%v", signalName, service.TS.TaskNum())
		}

		ext := base.GetAgentMetadata()
		ext["exitReason"] = exitReason
		ext["signalName"] = signalName
		ext["waitTimes"] = time.Now().Sub(start).String()
		if needExit {
			ctx, newSpan := tr.Start(exitCtx, "/agent/exit/ok", nil)
			tr.End(ctx, newSpan, ext, nil)
			break
		} else {
			if int(time.Now().Sub(start).Seconds())%30 == 0 {
				ctx, newSpan := tr.Start(exitCtx, "/agent/exit/continue", nil)
				tr.End(ctx, newSpan, ext, nil)
			}
		}
	}
	utils.TraceLog.LogServiceEvent(utils.LOG_EVENT_SERVICE_STOP, time.Now().Unix()-startTime.Unix(), utils.LL_INFO, types.RESULT_SUCCESS.Code)

	// SIGUSR1 由 running 容器发送：Agent 版本更新后 kill -10 agent，然后启动新的 agent
	if s != syscall.SIGUSR1 {
		service.WS.SetEnd()
	} else {
		time.Sleep(time.Second * 10)
	}
	tracer.ShutdownTracer()
	logs.Info("receive signal process done...")
}

func getRunningTaskInfo() map[string]interface{} {
	var detail = make(map[string]interface{})
	var taskNum = 0
	if runningTaskIdList := service.TS.TaskIdList(); runningTaskIdList != nil {
		taskNum = len(runningTaskIdList)
		detail["taskIds"] = runningTaskIdList
	}
	detail["taskNum"] = taskNum
	return detail
}

func RequestID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return mppUtils.GetRequestIdFromContext(ctx)
	}
}

func TaskID() log.Valuer {
	return func(ctx context.Context) interface{} {
		return mppUtils.GetTaskIdFromContext(ctx)
	}
}

// Caller returns a Valuer that returns a filename:line description of the caller.
func Caller(depth int) log.Valuer {
	return func(context.Context) interface{} {
		_, file, line, _ := runtime.Caller(depth)
		return filepath.Base(file) + ":" + strconv.Itoa(line)
	}
}
