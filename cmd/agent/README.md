

# Debug & Metrics
## 1. Debug: golang pprof
打开 pprof
```shell
curl -v 127.0.0.1:10881/pprof/on
```
访问 4050 端口
```shell
curl -v 127.0.0.1:4050/debug/pprof/
```
goroutine 示例
```shell
curl -s http://127.0.0.1:4050/debug/pprof/goroutine?debug=2
curl -s http://127.0.0.1:4050/debug/pprof/goroutine > goroutines.pprof
go tool pprof goroutines.pprof
```



## 2. Metrics: golang metrics
```shell
curl -v http://localhost:4040/metrics 
```



[//]: # ()
[//]: # ()
[//]: # (## todo )

[//]: # (1 增加grpc模式，通过 k8s pre-start 把进程提前拉起来，然后也不会影响main容器本身，然后agent开始交互，这个进程轻量级，只做很简单的事情，相当于目前agent的分身)

[//]: # ()
[//]: # (## v1.0.3)

[//]: # (1. todo 增加agent向多调度中心注册，也要支持可以管理多调度调度过来的任务，还要支持任务可以迁移到某一个调度上去？？？、)

[//]: # (2. 提升健壮性：Agent要增加本地嵌入式db缓存能力，防止异常重启或发布更新中的恢复&#40;当然需要engine支持断线重连等模式&#41;)
[//]: # ()
[//]: # (## v1.0.2 版本)

[//]: # (1. 增加超跑逻辑；)

[//]: # (2. 增加部署日志，任务预估时间，任务实际运行时间；)

[//]: # ()
[//]: # ()
[//]: # (## v1.0.1 版本)

[//]: # (1. 增加异常任务停止时的模型id；)

[//]: # ()
[//]: # ()
[//]: # (## 本地测试方法)

[//]: # (```)

[//]: # (export RUN_MODE=test)

[//]: # (export AGENT_CONFIG_PATH=${HOME}/app.conf)

[//]: # (go run cmd/agent/main.go)

[//]: # (```)

[//]: # ()
[//]: # ()
[//]: # (python无法捕获信号Bug:)

[//]: # ()
[//]: # (ssh -tt参数)

[//]: # (```shell)

[//]: # (https://stackoverflow.com/questions/46333657/send-signals-to-remote-process-with-python)

[//]: # ()
[//]: # (python3 /home/<USER>/algo/proxy.py，监听了SIGINT和SIGTERM)

[//]: # (```)

[//]: # ()
[//]: # (编译和构建：)

[//]: # (```shell)

[//]: # (export GOPROXY="https://mirrors.aliyun.com/goproxy/")

[//]: # (export GO111MODULE=on)

[//]: # (```)

[//]: # ()
[//]: # (打镜像（目前使用多阶段构建，包括编译和构建过程，具体见 `Dockerfile`）)

[//]: # (```shell)

[//]: # (sudo docker build -t registry.cn-shanghai.aliyuncs.com/aliyun-mps/mpp-worker-agent:1.0.2-pre .)

[//]: # (docker push registry.cn-shanghai.aliyuncs.com/aliyun-mps/mpp-worker-agent:1.0.2-pre)

[//]: # (```)

[//]: # (运行镜像：)

[//]: # (```shell)

[//]: # (docker run -it --rm --env-file ./env.list -v /var/run/docker.sock:/var/run/docker.sock ffa6b1825938)

[//]: # (```)

