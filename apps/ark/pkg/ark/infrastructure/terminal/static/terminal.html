<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Kubernetes Terminal</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/xterm@4.19.0/css/xterm.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #terminal {
            height: 400px;
            background-color: #000;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .or-divider {
            text-align: center;
            margin: 15px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kubernetes Terminal</h1>
        
        <div class="form-group">
            <label for="clusterId">Cluster ID:</label>
            <input type="text" id="clusterId" placeholder="Enter cluster ID">
        </div>
        
        <div class="form-group">
            <label for="containerId">Container ID:</label>
            <input type="text" id="containerId" placeholder="Enter container ID">
        </div>
        
        <div class="or-divider">OR</div>
        
        <div class="form-group">
            <label for="namespace">Namespace:</label>
            <input type="text" id="namespace" placeholder="Enter namespace (default: cse-default)">
        </div>
        
        <div class="form-group">
            <label for="podName">Pod Name:</label>
            <input type="text" id="podName" placeholder="Enter pod name">
        </div>
        
        <div class="form-group">
            <label for="containerName">Container Name:</label>
            <input type="text" id="containerName" placeholder="Enter container name">
        </div>
        
        <div class="form-group">
            <label for="shell">Shell:</label>
            <input type="text" id="shell" placeholder="Enter shell (default: auto-detect)">
        </div>
        
        <button id="connectBtn">Connect</button>
        
        <div id="terminal"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/xterm@4.19.0/lib/xterm.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.5.0/lib/xterm-addon-fit.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const term = new Terminal({
                cursorBlink: true,
                macOptionIsMeta: true,
                scrollback: 1000,
                fontSize: 14
            });
            
            const fitAddon = new FitAddon.FitAddon();
            term.loadAddon(fitAddon);
            
            term.open(document.getElementById('terminal'));
            fitAddon.fit();
            
            window.addEventListener('resize', () => {
                fitAddon.fit();
            });
            
            let socket = null;
            
            document.getElementById('connectBtn').addEventListener('click', function() {
                if (socket) {
                    socket.close();
                }
                
                const clusterId = document.getElementById('clusterId').value;
                const containerId = document.getElementById('containerId').value;
                const namespace = document.getElementById('namespace').value;
                const podName = document.getElementById('podName').value;
                const containerName = document.getElementById('containerName').value;
                const shell = document.getElementById('shell').value;
                
                if (!clusterId) {
                    term.writeln('\x1b[31mError: Cluster ID is required\x1b[0m');
                    return;
                }
                
                if (!containerId && (!podName || !containerName)) {
                    term.writeln('\x1b[31mError: Please provide either Container ID or both Pod Name and Container Name\x1b[0m');
                    return;
                }
                
                // Clear terminal
                term.clear();
                term.writeln('Connecting...');
                
                // Create terminal session
                let url = `/api/v1/terminal?cluster=${clusterId}`;
                
                if (containerId) {
                    url += `&container_id=${containerId}`;
                } else {
                    url += `&pod=${podName}&container=${containerName}`;
                    if (namespace) {
                        url += `&namespace=${namespace}`;
                    }
                }
                
                if (shell) {
                    url += `&shell=${shell}`;
                }
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        const sessionId = data.id;
                        term.writeln(`Session created: ${sessionId}`);
                        
                        // Connect to WebSocket
                        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                        socket = new WebSocket(`${protocol}//${window.location.host}/api/v1/terminal/ws?session=${sessionId}`);
                        
                        socket.onopen = function() {
                            term.writeln('\x1b[32mConnection established\x1b[0m');
                            
                            // Send terminal size
                            const size = { cols: term.cols, rows: term.rows };
                            socket.send(JSON.stringify({
                                op: 'resize',
                                cols: size.cols,
                                rows: size.rows
                            }));
                            
                            // Handle terminal input
                            term.onData(function(data) {
                                socket.send(JSON.stringify({
                                    op: 'stdin',
                                    data: data
                                }));
                            });
                            
                            // Handle terminal resize
                            term.onResize(function(size) {
                                socket.send(JSON.stringify({
                                    op: 'resize',
                                    cols: size.cols,
                                    rows: size.rows
                                }));
                            });
                        };
                        
                        socket.onmessage = function(event) {
                            const msg = JSON.parse(event.data);
                            
                            if (msg.op === 'stdout') {
                                term.write(msg.data);
                            }
                        };
                        
                        socket.onclose = function() {
                            term.writeln('\r\n\x1b[31mConnection closed\x1b[0m');
                        };
                        
                        socket.onerror = function(error) {
                            term.writeln(`\r\n\x1b[31mError: ${error.message}\x1b[0m`);
                        };
                    })
                    .catch(error => {
                        term.writeln(`\x1b[31mError: ${error.message}\x1b[0m`);
                    });
            });
        });
    </script>
</body>
</html>
