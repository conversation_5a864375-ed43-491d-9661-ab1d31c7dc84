name: Integration Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/analysis/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/analysis/**'

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    services:
      docker:
        image: docker:dind
        options: --privileged
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'
        
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
          
    - name: Install dependencies
      working-directory: apps/analysis
      run: go mod download
      
    - name: Run unit tests
      working-directory: apps/analysis
      run: make test
      
    - name: Run integration tests
      working-directory: apps/analysis
      run: make test-integration
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: apps/analysis/coverage.out
        flags: integration
        name: integration-tests
        fail_ci_if_error: false

  docker-integration-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Run integration tests in Docker
      working-directory: apps/analysis
      run: make docker-test
