package test

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// TestResult 测试结果结构
type TestResult struct {
	TestName    string        `json:"test_name"`
	Status      string        `json:"status"` // PASS, FAIL, SKIP
	Duration    time.Duration `json:"duration"`
	ErrorMsg    string        `json:"error_msg,omitempty"`
	Details     interface{}   `json:"details,omitempty"`
	Timestamp   time.Time     `json:"timestamp"`
}

// TestSuite 测试套件结果
type TestSuite struct {
	Name         string       `json:"name"`
	TotalTests   int          `json:"total_tests"`
	PassedTests  int          `json:"passed_tests"`
	FailedTests  int          `json:"failed_tests"`
	SkippedTests int          `json:"skipped_tests"`
	Duration     time.Duration `json:"duration"`
	Results      []TestResult `json:"results"`
	StartTime    time.Time    `json:"start_time"`
	EndTime      time.Time    `json:"end_time"`
}

// TestReport 完整的测试报告
type TestReport struct {
	ReportID        string      `json:"report_id"`
	GeneratedAt     time.Time   `json:"generated_at"`
	Environment     string      `json:"environment"`
	TestSuites      []TestSuite `json:"test_suites"`
	OverallSummary  Summary     `json:"overall_summary"`
	Configuration   Config      `json:"configuration"`
	DatabaseInfo    DBInfo      `json:"database_info"`
}

// Summary 总体摘要
type Summary struct {
	TotalSuites     int           `json:"total_suites"`
	TotalTests      int           `json:"total_tests"`
	TotalPassed     int           `json:"total_passed"`
	TotalFailed     int           `json:"total_failed"`
	TotalSkipped    int           `json:"total_skipped"`
	TotalDuration   time.Duration `json:"total_duration"`
	SuccessRate     float64       `json:"success_rate"`
	PerformanceInfo PerfInfo      `json:"performance_info"`
}

// Config 配置信息
type Config struct {
	GoVersion       string `json:"go_version"`
	TestFramework   string `json:"test_framework"`
	DatabaseType    string `json:"database_type"`
	ContainerEngine string `json:"container_engine"`
}

// DBInfo 数据库信息
type DBInfo struct {
	Type            string `json:"type"`
	Version         string `json:"version"`
	RecordsLoaded   int    `json:"records_loaded"`
	TablesCreated   int    `json:"tables_created"`
	DataSourceFile  string `json:"data_source_file"`
}

// PerfInfo 性能信息
type PerfInfo struct {
	AverageResponseTime time.Duration `json:"average_response_time"`
	MaxResponseTime     time.Duration `json:"max_response_time"`
	MinResponseTime     time.Duration `json:"min_response_time"`
	ThroughputRPS       float64       `json:"throughput_rps"`
}

// TestReportGenerator 测试报告生成器
type TestReportGenerator struct {
	report    *TestReport
	startTime time.Time
}

// NewTestReportGenerator 创建新的测试报告生成器
func NewTestReportGenerator() *TestReportGenerator {
	return &TestReportGenerator{
		report: &TestReport{
			ReportID:    fmt.Sprintf("test-report-%d", time.Now().Unix()),
			GeneratedAt: time.Now(),
			Environment: "test",
			Configuration: Config{
				TestFramework:   "testify + testcontainers",
				DatabaseType:    "MySQL",
				ContainerEngine: "Docker",
			},
			DatabaseInfo: DBInfo{
				Type:           "MySQL",
				Version:        "8.0",
				DataSourceFile: "scripts/data.sql",
				RecordsLoaded:  137, // 基于data.sql中的记录数
				TablesCreated:  1,   // mps_engine_quota_config表
			},
		},
		startTime: time.Now(),
	}
}

// AddTestSuite 添加测试套件结果
func (g *TestReportGenerator) AddTestSuite(suite TestSuite) {
	g.report.TestSuites = append(g.report.TestSuites, suite)
}

// GenerateReport 生成最终报告
func (g *TestReportGenerator) GenerateReport() *TestReport {
	// 计算总体摘要
	var totalTests, totalPassed, totalFailed, totalSkipped int
	var totalDuration time.Duration
	var responseTimes []time.Duration

	for _, suite := range g.report.TestSuites {
		totalTests += suite.TotalTests
		totalPassed += suite.PassedTests
		totalFailed += suite.FailedTests
		totalSkipped += suite.SkippedTests
		totalDuration += suite.Duration

		// 收集响应时间数据
		for _, result := range suite.Results {
			if result.Status == "PASS" {
				responseTimes = append(responseTimes, result.Duration)
			}
		}
	}

	// 计算性能指标
	var avgResponseTime, maxResponseTime, minResponseTime time.Duration
	if len(responseTimes) > 0 {
		var total time.Duration
		maxResponseTime = responseTimes[0]
		minResponseTime = responseTimes[0]

		for _, rt := range responseTimes {
			total += rt
			if rt > maxResponseTime {
				maxResponseTime = rt
			}
			if rt < minResponseTime {
				minResponseTime = rt
			}
		}
		avgResponseTime = total / time.Duration(len(responseTimes))
	}

	// 计算吞吐量（每秒请求数）
	throughput := 0.0
	if totalDuration > 0 {
		throughput = float64(totalPassed) / totalDuration.Seconds()
	}

	// 计算成功率
	successRate := 0.0
	if totalTests > 0 {
		successRate = float64(totalPassed) / float64(totalTests) * 100
	}

	g.report.OverallSummary = Summary{
		TotalSuites:   len(g.report.TestSuites),
		TotalTests:    totalTests,
		TotalPassed:   totalPassed,
		TotalFailed:   totalFailed,
		TotalSkipped:  totalSkipped,
		TotalDuration: totalDuration,
		SuccessRate:   successRate,
		PerformanceInfo: PerfInfo{
			AverageResponseTime: avgResponseTime,
			MaxResponseTime:     maxResponseTime,
			MinResponseTime:     minResponseTime,
			ThroughputRPS:       throughput,
		},
	}

	return g.report
}

// SaveReportToFile 保存报告到文件
func (g *TestReportGenerator) SaveReportToFile(filename string) error {
	report := g.GenerateReport()
	
	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// 保存JSON格式
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal report: %w", err)
	}

	if err := os.WriteFile(filename, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write report file: %w", err)
	}

	return nil
}

// SaveHTMLReport 保存HTML格式的报告
func (g *TestReportGenerator) SaveHTMLReport(filename string) error {
	report := g.GenerateReport()
	
	htmlContent := g.generateHTMLContent(report)
	
	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	if err := os.WriteFile(filename, []byte(htmlContent), 0644); err != nil {
		return fmt.Errorf("failed to write HTML report file: %w", err)
	}

	return nil
}

// generateHTMLContent 生成HTML内容
func (g *TestReportGenerator) generateHTMLContent(report *TestReport) string {
	html := fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPS Job Analysis API 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .skip { color: #ffc107; }
        .test-suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; }
        .test-suite-header { background: #e9ecef; padding: 15px; font-weight: bold; }
        .test-result { padding: 10px 15px; border-bottom: 1px solid #eee; }
        .test-result:last-child { border-bottom: none; }
        .status-badge { padding: 3px 8px; border-radius: 3px; color: white; font-size: 12px; }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-skip { background-color: #ffc107; }
        .performance-info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MPS Job Analysis API 测试报告</h1>
            <p>报告ID: %s</p>
            <p>生成时间: %s</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="value">%d</div>
            </div>
            <div class="summary-card">
                <h3>通过</h3>
                <div class="value pass">%d</div>
            </div>
            <div class="summary-card">
                <h3>失败</h3>
                <div class="value fail">%d</div>
            </div>
            <div class="summary-card">
                <h3>跳过</h3>
                <div class="value skip">%d</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value">%.1f%%</div>
            </div>
            <div class="summary-card">
                <h3>总耗时</h3>
                <div class="value">%v</div>
            </div>
        </div>`,
		report.ReportID,
		report.GeneratedAt.Format("2006-01-02 15:04:05"),
		report.OverallSummary.TotalTests,
		report.OverallSummary.TotalPassed,
		report.OverallSummary.TotalFailed,
		report.OverallSummary.TotalSkipped,
		report.OverallSummary.SuccessRate,
		report.OverallSummary.TotalDuration,
	)

	// 添加性能信息
	html += fmt.Sprintf(`
        <div class="performance-info">
            <h3>性能指标</h3>
            <p><strong>平均响应时间:</strong> %v</p>
            <p><strong>最大响应时间:</strong> %v</p>
            <p><strong>最小响应时间:</strong> %v</p>
            <p><strong>吞吐量:</strong> %.2f RPS</p>
        </div>`,
		report.OverallSummary.PerformanceInfo.AverageResponseTime,
		report.OverallSummary.PerformanceInfo.MaxResponseTime,
		report.OverallSummary.PerformanceInfo.MinResponseTime,
		report.OverallSummary.PerformanceInfo.ThroughputRPS,
	)

	// 添加测试套件详情
	for _, suite := range report.TestSuites {
		html += fmt.Sprintf(`
        <div class="test-suite">
            <div class="test-suite-header">
                %s - %d/%d 通过 (耗时: %v)
            </div>`,
			suite.Name,
			suite.PassedTests,
			suite.TotalTests,
			suite.Duration,
		)

		for _, result := range suite.Results {
			statusClass := "status-" + result.Status
			if result.Status == "PASS" {
				statusClass = "status-pass"
			} else if result.Status == "FAIL" {
				statusClass = "status-fail"
			} else {
				statusClass = "status-skip"
			}

			html += fmt.Sprintf(`
            <div class="test-result">
                <span class="status-badge %s">%s</span>
                <strong>%s</strong> (耗时: %v)
                %s
            </div>`,
				statusClass,
				result.Status,
				result.TestName,
				result.Duration,
				func() string {
					if result.ErrorMsg != "" {
						return fmt.Sprintf("<br><span style='color: #dc3545;'>错误: %s</span>", result.ErrorMsg)
					}
					return ""
				}(),
			)
		}

		html += `</div>`
	}

	html += fmt.Sprintf(`
        <div class="footer">
            <p>测试环境: %s | 数据库: %s %s | 数据源: %s</p>
            <p>测试框架: %s | 容器引擎: %s</p>
        </div>
    </div>
</body>
</html>`,
		report.Environment,
		report.DatabaseInfo.Type,
		report.DatabaseInfo.Version,
		report.DatabaseInfo.DataSourceFile,
		report.Configuration.TestFramework,
		report.Configuration.ContainerEngine,
	)

	return html
}
