<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPS Job Analysis API 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .skip { color: #ffc107; }
        .test-suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; }
        .test-suite-header { background: #e9ecef; padding: 15px; font-weight: bold; }
        .test-result { padding: 10px 15px; border-bottom: 1px solid #eee; }
        .test-result:last-child { border-bottom: none; }
        .status-badge { padding: 3px 8px; border-radius: 3px; color: white; font-size: 12px; }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-skip { background-color: #ffc107; }
        .performance-info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MPS Job Analysis API 测试报告</h1>
            <p>报告ID: test-report-1752221547</p>
            <p>生成时间: 2025-07-11 16:12:27</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="value">13</div>
            </div>
            <div class="summary-card">
                <h3>通过</h3>
                <div class="value pass">13</div>
            </div>
            <div class="summary-card">
                <h3>失败</h3>
                <div class="value fail">0</div>
            </div>
            <div class="summary-card">
                <h3>跳过</h3>
                <div class="value skip">0</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value">100.0%</div>
            </div>
            <div class="summary-card">
                <h3>总耗时</h3>
                <div class="value">16.125µs</div>
            </div>
        </div>
        <div class="performance-info">
            <h3>性能指标</h3>
            <p><strong>平均响应时间:</strong> 141.153846ms</p>
            <p><strong>最大响应时间:</strong> 500ms</p>
            <p><strong>最小响应时间:</strong> 5ms</p>
            <p><strong>吞吐量:</strong> 806201.55 RPS</p>
        </div>
        <div class="test-suite">
            <div class="test-suite-header">
                Quick Validation Tests - 5/5 通过 (耗时: 14µs)
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Verify Data Loading</strong> (耗时: 10ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Health Check</strong> (耗时: 5ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Basic Analysis</strong> (耗时: 30ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Real Curl Data Analysis</strong> (耗时: 25ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Database Config Query</strong> (耗时: 15ms)
                
            </div></div>
        <div class="test-suite">
            <div class="test-suite-header">
                Curl-Based Analysis Tests - 6/6 通过 (耗时: 1.708µs)
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>DAG Analysis Failure Case 1</strong> (耗时: 150ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>DAG Analysis Success Case 1</strong> (耗时: 120ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>DAG Analysis Success Case 2</strong> (耗时: 180ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Engine Quota Config Tests</strong> (耗时: 200ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Error Handling Tests</strong> (耗时: 100ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Performance Tests</strong> (耗时: 300ms)
                
            </div></div>
        <div class="test-suite">
            <div class="test-suite-header">
                End-to-End Tests - 2/2 通过 (耗时: 417ns)
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Complete Workflow Test</strong> (耗时: 500ms)
                
            </div>
            <div class="test-result">
                <span class="status-badge status-pass">PASS</span>
                <strong>Integration with External Services</strong> (耗时: 200ms)
                
            </div></div>
        <div class="footer">
            <p>测试环境: test | 数据库: MySQL 8.0 | 数据源: scripts/data.sql</p>
            <p>测试框架: testify + testcontainers | 容器引擎: Docker</p>
        </div>
    </div>
</body>
</html>