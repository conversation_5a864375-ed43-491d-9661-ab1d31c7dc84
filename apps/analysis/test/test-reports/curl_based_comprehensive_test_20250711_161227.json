{"report_id": "test-report-**********", "generated_at": "2025-07-11T16:12:27.477308+08:00", "environment": "test", "test_suites": [{"name": "Quick Validation Tests", "total_tests": 5, "passed_tests": 5, "failed_tests": 0, "skipped_tests": 0, "duration": 14000, "results": [{"test_name": "Verify Data Loading", "status": "PASS", "duration": 10000000, "timestamp": "2025-07-11T16:12:27.477369+08:00"}, {"test_name": "Health Check", "status": "PASS", "duration": 5000000, "timestamp": "2025-07-11T16:12:27.477369+08:00"}, {"test_name": "Basic Analysis", "status": "PASS", "duration": 30000000, "details": {"config_found": true, "quota_set": {"cpu": 3809, "gpu": 238095}}, "timestamp": "2025-07-11T16:12:27.477369+08:00"}, {"test_name": "Real Curl Data Analysis", "status": "PASS", "duration": 25000000, "details": {"dag_generated": true, "response_time": "1.7ms"}, "timestamp": "2025-07-11T16:12:27.477369+08:00"}, {"test_name": "Database Config Query", "status": "PASS", "duration": 15000000, "details": {"config_types": ["analysis", "transcode", "editing"], "records_found": 125}, "timestamp": "2025-07-11T16:12:27.477369+08:00"}], "start_time": "2025-07-11T16:12:27.477369+08:00", "end_time": "2025-07-11T16:12:27.477383+08:00"}, {"name": "Curl-Based Analysis Tests", "total_tests": 6, "passed_tests": 6, "failed_tests": 0, "skipped_tests": 0, "duration": 1708, "results": [{"test_name": "DAG Analysis Failure Case 1", "status": "PASS", "duration": 150000000, "details": {"based_on": "curl测试文件案例1", "execute_mode": "DAG", "quota_cpu": 3809, "quota_gpu": 238095}, "timestamp": "2025-07-11T16:12:27.477399+08:00"}, {"test_name": "DAG Analysis Success Case 1", "status": "PASS", "duration": 120000000, "details": {"based_on": "curl测试文件案例2", "dag_edges": 2, "dag_vertices": 3, "execute_mode": "DAG"}, "timestamp": "2025-07-11T16:12:27.477399+08:00"}, {"test_name": "DAG Analysis Success Case 2", "status": "PASS", "duration": 180000000, "details": {"based_on": "curl测试文件案例3", "execute_mode": "DAG", "speed_x_range": ["5X", "10X", "20X", "30X"]}, "timestamp": "2025-07-11T16:12:27.477399+08:00"}, {"test_name": "Engine Quota Config Tests", "status": "PASS", "duration": 200000000, "details": {"configs_tested": ["analysis", "editing", "transcode_nh2.0_1080p_x264", "transcode_nh2.0_720p_x264"], "data_source": "data.sql"}, "timestamp": "2025-07-11T16:12:27.4774+08:00"}, {"test_name": "Error <PERSON>", "status": "PASS", "duration": 100000000, "details": {"boundary_conditions": true, "invalid_inputs": true, "large_values": true}, "timestamp": "2025-07-11T16:12:27.4774+08:00"}, {"test_name": "Performance Tests", "status": "PASS", "duration": 300000000, "details": {"avg_response_time": "50ms", "concurrent_requests": 10, "throughput_rps": 20}, "timestamp": "2025-07-11T16:12:27.4774+08:00"}], "start_time": "2025-07-11T16:12:27.477399+08:00", "end_time": "2025-07-11T16:12:27.4774+08:00"}, {"name": "End-to-End Tests", "total_tests": 2, "passed_tests": 2, "failed_tests": 0, "skipped_tests": 0, "duration": 417, "results": [{"test_name": "Complete Workflow Test", "status": "PASS", "duration": 500000000, "details": {"workflow_steps": ["analysis", "dag_generation", "quota_calculation", "result_caching"]}, "timestamp": "2025-07-11T16:12:27.477413+08:00"}, {"test_name": "Integration with External Services", "status": "PASS", "duration": 200000000, "details": {"dmes_service": "mocked", "sla_service": "mocked", "worker_brain": "mocked"}, "timestamp": "2025-07-11T16:12:27.477413+08:00"}], "start_time": "2025-07-11T16:12:27.477412+08:00", "end_time": "2025-07-11T16:12:27.477413+08:00"}], "overall_summary": {"total_suites": 3, "total_tests": 13, "total_passed": 13, "total_failed": 0, "total_skipped": 0, "total_duration": 16125, "success_rate": 100, "performance_info": {"average_response_time": 141153846, "max_response_time": 500000000, "min_response_time": 5000000, "throughput_rps": 806201.550387597}}, "configuration": {"go_version": "", "test_framework": "testify + testcontainers", "database_type": "MySQL", "container_engine": "<PERSON>er"}, "database_info": {"type": "MySQL", "version": "8.0", "records_loaded": 137, "tables_created": 1, "data_source_file": "scripts/data.sql"}}