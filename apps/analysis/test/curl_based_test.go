package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	kratoshttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
	mysqldriver "gorm.io/driver/mysql"
	"gorm.io/gorm"

	v1 "mps-job-analysis-go/api/job/v1"
	"mps-job-analysis-go/internal/biz"
	"mps-job-analysis-go/internal/data"
	"mps-job-analysis-go/internal/service"
)

// CurlBasedTestSuite 基于curl测试文件的测试套件
type CurlBasedTestSuite struct {
	suite.Suite
	mysqlContainer testcontainers.Container
	httpServer     *kratoshttp.Server
	baseURL        string
	ctx            context.Context
}

// SetupSuite 在所有测试开始前运行
func (suite *CurlBasedTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 启动 MySQL 容器
	mysqlContainer, err := mysql.Run(suite.ctx,
		"mysql:8.0",
		mysql.WithDatabase("testdb"),
		mysql.WithUsername("testuser"),
		mysql.WithPassword("testpass"),
		mysql.WithScripts(filepath.Join("..", "scripts", "data.sql")),
	)
	require.NoError(suite.T(), err)

	suite.mysqlContainer = mysqlContainer

	// 获取连接信息
	host, err := mysqlContainer.Host(suite.ctx)
	require.NoError(suite.T(), err)

	port, err := mysqlContainer.MappedPort(suite.ctx, "3306")
	require.NoError(suite.T(), err)

	// 创建数据库连接
	dsn := fmt.Sprintf("testuser:testpass@tcp(%s:%s)/testdb?charset=utf8mb4&parseTime=True&loc=Local",
		host, port.Port())

	db, err := gorm.Open(mysqldriver.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)

	// 创建数据访问层 - 使用NewData函数创建
	dataLayer, cleanup, err := data.NewData(nil, log.DefaultLogger, db, nil)
	require.NoError(suite.T(), err)
	defer cleanup()

	// 创建仓库
	jobAnalysisRepo := data.NewJobAnalysisRepo(dataLayer, log.DefaultLogger)
	externalServiceRepo := &CurlMockExternalServiceRepo{}

	// 创建业务逻辑层
	jobAnalysisUsecase := biz.NewJobAnalysisUsecase(jobAnalysisRepo, externalServiceRepo, log.DefaultLogger)

	// 创建服务层
	jobAnalysisService := service.NewJobAnalysisService(jobAnalysisUsecase, log.DefaultLogger)

	// 创建 HTTP 服务器
	suite.httpServer = kratoshttp.NewServer(
		kratoshttp.Address("127.0.0.1:0"), // 使用随机端口
		kratoshttp.Middleware(
			recovery.Recovery(),
			logging.Server(log.DefaultLogger),
		),
	)

	// 注册路由
	v1.RegisterJobAnalysisServiceHTTPServer(suite.httpServer, jobAnalysisService)
	v1.RegisterServiceHealthServiceHTTPServer(suite.httpServer, jobAnalysisService)

	// 启动服务器
	go func() {
		if err := suite.httpServer.Start(suite.ctx); err != nil {
			suite.T().Logf("HTTP server start error: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 获取服务器地址
	endpoint, err := suite.httpServer.Endpoint()
	require.NoError(suite.T(), err)
	suite.baseURL = fmt.Sprintf("http://%s", endpoint.Host)
}

// TearDownSuite 在所有测试结束后运行
func (suite *CurlBasedTestSuite) TearDownSuite() {
	if suite.httpServer != nil {
		suite.httpServer.Stop(suite.ctx)
	}
	if suite.mysqlContainer != nil {
		err := suite.mysqlContainer.Terminate(suite.ctx)
		require.NoError(suite.T(), err)
	}
}

// CurlMockExternalServiceRepo 模拟外部服务仓库（避免与e2e_test.go中的重复）
type CurlMockExternalServiceRepo struct{}

func (m *CurlMockExternalServiceRepo) CallDMES(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success": true,
		"quota_set": map[string]int64{
			"cpu":  1500,
			"disk": 2000,
		},
	}, nil
}

func (m *CurlMockExternalServiceRepo) CallWorkerBrain(ctx context.Context, engineModel string, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success": true,
		"quota_set": map[string]int64{
			"cpu": 3809,
			"gpu": 238095,
		},
		"expect_cost_time": 296,
	}, nil
}

func (m *CurlMockExternalServiceRepo) CallSLA(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success":             true,
		"sla_finish_delay":    60,
		"sla_queuing_delay":   30,
		"max_migrate_retry":   5,
	}, nil
}

func (m *CurlMockExternalServiceRepo) CallMediaMeta(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success":  true,
		"duration": 120.5,
		"width":    1920,
		"height":   1080,
		"bitrate":  5000000,
	}, nil
}

// TestDAGAnalysisFailureCase1 测试DAG分析失败案例1
// 基于curl分析接口测试.txt中的第一个测试用例
func (suite *CurlBasedTestSuite) TestDAGAnalysisFailureCase1() {
	// 构建请求体，基于curl测试文件中的第一个案例
	requestBody := map[string]interface{}{
		"product":                  "mps",
		"engineModel":              "mps-transcode-new",
		"sliceProcess":             true,
		"userId":                   "1207704461481212",
		"jobId":                    "8db9344b535d4bf69d0483a15e9352ed",
		"autoSpeedX":               false,
		"createTime":               1726979034400,
		"requestId":                "915FF26C-938A-4F87-A7B5-10A50EAF3466",
		"analysisMode":             "byScheduleParams",
		"tag":                      "asi-v100",
		"useWorkerBrainResult":     true,
		"invokeWorkerBrain":        true,
		"scheduleParams": map[string]interface{}{
			"pipelineId":                   "2c416551f7564aa48453a4ad9279f534",
			"priority":                     6,
			"scheduleLevel":                "BOOST",
			"speedXRange":                  []string{"5X"},
			"multiSpeedDowngradePolicy":    "NORMALSPEED",
			"inputs": []map[string]interface{}{
				{
					"duration":      50.388333,
					"videoBitrate":  4101.043,
					"avgFps":        29.97003,
					"size":          27455025,
					"format":        "QuickTime / MOV",
					"fps":           29.97003,
					"width":         1920,
					"audioCodec":    "aac",
					"audioBitrate":  253.375,
					"height":        1080,
					"videoCodec":    "h264",
				},
			},
			"configs": []map[string]interface{}{
				{
					"duration":     50.388333,
					"nhVersion":    "2.0",
					"format":       "mp4",
					"fps":          29.97003,
					"id":           "1f37c32bd61b4b4c9887128ecceb96fc",
					"audioCodec":   "fdk_aac",
					"jobType":      "transcode",
					"templateId":   "9977145f81284d5caea1fa2060308704",
					"audioBitrate": 64.0,
					"videoCodec":   "x264",
				},
			},
		},
		"trace": map[string]interface{}{
			"jobId":     "1f37c32bd61b4b4c9887128ecceb96fc",
			"aliyunUid": "1207704461481212",
			"requestId": "1CBF85DC-60C2-5E1B-85A2-022BC581FD8D",
		},
	}

	suite.executeAnalysisTest("DAG Analysis Failure Case 1", requestBody, func(resp *v1.AnalyzeJobResponse) {
		// 验证响应
		assert.True(suite.T(), resp.Success)
		assert.Equal(suite.T(), "Success", resp.Code)
		
		// 验证配额设置 - 基于data.sql中的transcode_nh2.0_1080p_x264配置
		assert.NotEmpty(suite.T(), resp.QuotaSet)
		assert.Contains(suite.T(), resp.QuotaSet, "cpu")
		assert.Contains(suite.T(), resp.QuotaSet, "gpu")
		
		// 验证期望耗时
		assert.Greater(suite.T(), resp.ExpectCostTime, int64(0))
		
		// 验证SpeedX消息
		assert.NotEmpty(suite.T(), resp.SpeedXMessage)
		if speedXMsg, exists := resp.SpeedXMessage["5X"]; exists {
			assert.Equal(suite.T(), int32(3), speedXMsg.Code)
			assert.Equal(suite.T(), "noSupportParallel", speedXMsg.Message)
		}
	})
}

// TestDAGAnalysisSuccessCase1 测试DAG分析成功案例1
// 基于curl分析接口测试.txt中的第二个测试用例
func (suite *CurlBasedTestSuite) TestDAGAnalysisSuccessCase1() {
	requestBody := map[string]interface{}{
		"product":                  "mps",
		"engineModel":              "mps-transcode-new",
		"sliceProcess":             true,
		"userId":                   "1207704461481212",
		"jobId":                    "72ba510966b041c8a89815911e22f0fc",
		"autoSpeedX":               false,
		"createTime":               1727070394406,
		"requestId":                "A9E2595F-91A1-4A25-BD09-94E6B0EBA5FA",
		"analysisMode":             "byScheduleParams",
		"tag":                      "asi-v100",
		"useWorkerBrainResult":     true,
		"invokeWorkerBrain":        true,
		"scheduleParams": map[string]interface{}{
			"pipelineId":                   "2c416551f7564aa48453a4ad9279f534",
			"priority":                     6,
			"scheduleLevel":                "BOOST",
			"speedXRange":                  []string{"5X"},
			"multiSpeedDowngradePolicy":    "NORMALSPEED",
			"inputs": []map[string]interface{}{
				{
					"duration":      159.837,
					"videoBitrate":  810.193,
					"avgFps":        25.006262,
					"size":          18868101,
					"format":        "QuickTime / MOV",
					"fps":           25.0,
					"width":         960,
					"audioCodec":    "aac",
					"audioBitrate":  128.045,
					"height":        540,
					"videoCodec":    "h264",
				},
			},
			"configs": []map[string]interface{}{
				{
					"duration":     159.837,
					"nhVersion":    "2.0",
					"format":       "mp4",
					"fps":          25.006262,
					"id":           "5cd1c82c839947d9be9288107bd2e6ec",
					"audioCodec":   "fdk_aac",
					"jobType":      "transcode",
					"templateId":   "9c2bcfe2a2264dd5a177b3115276b78b",
					"audioBitrate": 64.0,
					"videoCodec":   "x264",
				},
			},
		},
		"trace": map[string]interface{}{
			"jobId":     "5cd1c82c839947d9be9288107bd2e6ec",
			"aliyunUid": "1207704461481212",
			"requestId": "88553C68-EC74-5ADA-95C9-8B8BAF359CD2",
		},
	}

	suite.executeAnalysisTest("DAG Analysis Success Case 1", requestBody, func(resp *v1.AnalyzeJobResponse) {
		// 验证响应
		assert.True(suite.T(), resp.Success)
		assert.Equal(suite.T(), "Success", resp.Code)
		assert.Equal(suite.T(), v1.ExecuteMode_DAG, resp.ExecuteMode)

		// 验证DAG图结构
		assert.NotNil(suite.T(), resp.Graph)
		assert.NotEmpty(suite.T(), resp.Graph.JobVertexs)
		assert.NotEmpty(suite.T(), resp.Graph.JobEdges)

		// 验证配额设置
		assert.NotEmpty(suite.T(), resp.QuotaSet)
		assert.Contains(suite.T(), resp.QuotaSet, "cpu")
		assert.Contains(suite.T(), resp.QuotaSet, "gpu")
		assert.Contains(suite.T(), resp.QuotaSet, "disk")
	})
}

// TestDAGAnalysisSuccessCase2 测试DAG分析成功案例2
// 基于curl分析接口测试.txt中的第三个测试用例
func (suite *CurlBasedTestSuite) TestDAGAnalysisSuccessCase2() {
	requestBody := map[string]interface{}{
		"product":                  "mps",
		"engineModel":              "mps-transcode-new",
		"sliceProcess":             true,
		"userId":                   "1833220977560785",
		"jobId":                    "ac87c69b53b64d3c911c3d7bebd5100c",
		"autoSpeedX":               false,
		"createTime":               1726659255291,
		"requestId":                "DFFD920C-12EA-408C-B589-E342A3D96C05",
		"analysisMode":             "byScheduleParams",
		"tag":                      "asi-v100",
		"useWorkerBrainResult":     true,
		"invokeWorkerBrain":        true,
		"scheduleParams": map[string]interface{}{
			"pipelineId":                   "88af4ced606343d9a90506632d03317f",
			"priority":                     6,
			"scheduleLevel":                "BOOST",
			"speedXRange":                  []string{"5X", "10X", "20X", "30X"},
			"multiSpeedDowngradePolicy":    "NORMALSPEED",
			"inputs": []map[string]interface{}{
				{
					"duration":      3759.334,
					"videoBitrate":  1818.734,
					"avgFps":        30.0,
					"size":          918883478,
					"format":        "QuickTime / MOV",
					"fps":           30.0,
					"width":         1280,
					"audioCodec":    "aac",
					"audioBitrate":  127.994,
					"height":        720,
					"videoCodec":    "h264",
				},
			},
			"configs": []map[string]interface{}{
				{
					"duration":     3759.334,
					"nhVersion":    "2.0",
					"format":       "mp4",
					"fps":          30.0,
					"id":           "34d9d87b1d5740d88d1c44692167e6ea",
					"audioCodec":   "fdk_aac",
					"jobType":      "transcode",
					"templateId":   "5235cd661b9944d0b1209bd9936969a9",
					"audioBitrate": 64.0,
					"videoCodec":   "x264",
				},
			},
		},
		"trace": map[string]interface{}{
			"jobId":     "34d9d87b1d5740d88d1c44692167e6ea",
			"aliyunUid": "1833220977560785",
			"requestId": "B89469B1-B63E-1C88-B818-F4A57C14B2C6",
		},
	}

	suite.executeAnalysisTest("DAG Analysis Success Case 2", requestBody, func(resp *v1.AnalyzeJobResponse) {
		// 验证响应
		assert.True(suite.T(), resp.Success)
		assert.Equal(suite.T(), "Success", resp.Code)
		assert.Equal(suite.T(), v1.ExecuteMode_DAG, resp.ExecuteMode)

		// 验证DAG图结构 - 这个案例应该生成复杂的DAG图
		assert.NotNil(suite.T(), resp.Graph)
		assert.NotEmpty(suite.T(), resp.Graph.JobVertexs)
		assert.NotEmpty(suite.T(), resp.Graph.JobEdges)

		// 验证多个顶点（应该有多个处理节点）
		assert.GreaterOrEqual(suite.T(), len(resp.Graph.JobVertexs), 3)

		// 验证配额设置
		assert.NotEmpty(suite.T(), resp.QuotaSet)
		assert.Contains(suite.T(), resp.QuotaSet, "cpu")
		assert.Contains(suite.T(), resp.QuotaSet, "gpu")
		assert.Contains(suite.T(), resp.QuotaSet, "disk")
	})
}

// executeAnalysisTest 执行分析测试的辅助方法
func (suite *CurlBasedTestSuite) executeAnalysisTest(testName string, requestBody map[string]interface{}, validator func(*v1.AnalyzeJobResponse)) {
	suite.Run(testName, func() {
		jsonBody, err := json.Marshal(requestBody)
		require.NoError(suite.T(), err)

		resp, err := http.Post(
			suite.baseURL+"/job/analysis",
			"application/json",
			bytes.NewBuffer(jsonBody),
		)
		require.NoError(suite.T(), err)
		defer resp.Body.Close()

		assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

		body, err := io.ReadAll(resp.Body)
		require.NoError(suite.T(), err)

		var analysisResp v1.AnalyzeJobResponse
		err = json.Unmarshal(body, &analysisResp)
		require.NoError(suite.T(), err)

		// 执行自定义验证
		validator(&analysisResp)
	})
}

// TestEngineQuotaConfigBasedTests 基于data.sql中的引擎配额配置进行测试
func (suite *CurlBasedTestSuite) TestEngineQuotaConfigBasedTests() {
	// 测试用例：基于data.sql中的不同配置
	testCases := []struct {
		name        string
		configName  string
		requestBody map[string]interface{}
		validator   func(*v1.AnalyzeJobResponse)
	}{
		{
			name:       "Analysis Job Type",
			configName: "analysis",
			requestBody: map[string]interface{}{
				"product":      "mps",
				"engineModel":  "analysis", // 使用实际存在的配置名称
				"userId":       "test-user-analysis",
				"jobId":        "test-job-analysis",
				"analysisMode": "byParseEngineParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType": "analysis",
						},
					},
				},
			},
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.True(suite.T(), resp.Success)
				assert.Contains(suite.T(), resp.QuotaSet, "cpu")
				assert.Equal(suite.T(), int64(1000), resp.QuotaSet["cpu"]) // 基于data.sql中的配置
			},
		},
		{
			name:       "Editing Job Type",
			configName: "editing",
			requestBody: map[string]interface{}{
				"product":      "mps",
				"engineModel":  "editing", // 使用实际存在的配置名称
				"userId":       "test-user-editing",
				"jobId":        "test-job-editing",
				"analysisMode": "byParseEngineParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType": "editing",
						},
					},
				},
			},
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.True(suite.T(), resp.Success)
				assert.Contains(suite.T(), resp.QuotaSet, "cpu")
				assert.Equal(suite.T(), int64(8000), resp.QuotaSet["cpu"]) // 基于data.sql中的配置
			},
		},
		{
			name:       "Transcode NH2.0 1080p x264",
			configName: "transcode_nh2.0_1080p_x264",
			requestBody: map[string]interface{}{
				"product":      "mps",
				"engineModel":  "transcode_nh2.0_1080p_x264", // 使用实际存在的配置名称
				"userId":       "test-user-transcode",
				"jobId":        "test-job-transcode-nh2.0",
				"analysisMode": "byParseEngineParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType":    "transcode",
							"nhVersion":  "2.0",
							"videoCodec": "x264",
						},
					},
					"inputs": []map[string]interface{}{
						{
							"width":  1920,
							"height": 1080,
						},
					},
				},
			},
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.True(suite.T(), resp.Success)
				assert.Contains(suite.T(), resp.QuotaSet, "cpu")
				assert.Contains(suite.T(), resp.QuotaSet, "gpu")
				assert.Equal(suite.T(), int64(3809), resp.QuotaSet["cpu"])    // 基于data.sql中的配置
				assert.Equal(suite.T(), int64(238095), resp.QuotaSet["gpu"]) // 基于data.sql中的配置
			},
		},
		{
			name:       "Transcode NH2.0 720p x264",
			configName: "transcode_nh2.0_720p_x264",
			requestBody: map[string]interface{}{
				"product":      "mps",
				"engineModel":  "transcode_nh2.0_720p_x264", // 使用实际存在的配置名称
				"userId":       "test-user-transcode-720p",
				"jobId":        "test-job-transcode-720p",
				"analysisMode": "byParseEngineParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType":    "transcode",
							"nhVersion":  "2.0",
							"videoCodec": "x264",
						},
					},
					"inputs": []map[string]interface{}{
						{
							"width":  1280,
							"height": 720,
						},
					},
				},
			},
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.True(suite.T(), resp.Success)
				assert.Contains(suite.T(), resp.QuotaSet, "cpu")
				assert.Contains(suite.T(), resp.QuotaSet, "gpu")
				assert.Equal(suite.T(), int64(3809), resp.QuotaSet["cpu"])    // 基于data.sql中的配置
				assert.Equal(suite.T(), int64(238095), resp.QuotaSet["gpu"]) // 基于data.sql中的配置
			},
		},
	}

	for _, tc := range testCases {
		suite.executeAnalysisTest(tc.name, tc.requestBody, tc.validator)
	}
}

// TestErrorHandlingAndBoundaryConditions 测试错误处理和边界条件
func (suite *CurlBasedTestSuite) TestErrorHandlingAndBoundaryConditions() {
	testCases := []struct {
		name        string
		requestBody map[string]interface{}
		expectError bool
		validator   func(*v1.AnalyzeJobResponse)
	}{
		{
			name: "Missing Engine Model",
			requestBody: map[string]interface{}{
				"userId":       "test-user",
				"jobId":        "test-job-missing-engine",
				"analysisMode": "byParseEngineParams",
			},
			expectError: false, // 服务应该返回错误响应而不是HTTP错误
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.False(suite.T(), resp.Success)
				assert.NotEqual(suite.T(), "Success", resp.Code)
			},
		},
		{
			name: "Invalid Analysis Mode",
			requestBody: map[string]interface{}{
				"engineModel":  "transcode_nh2.0_1080p_x264",
				"userId":       "test-user",
				"jobId":        "test-job-invalid-mode",
				"analysisMode": "invalidMode",
			},
			expectError: false,
			validator: func(resp *v1.AnalyzeJobResponse) {
				assert.False(suite.T(), resp.Success)
				assert.NotEqual(suite.T(), "Success", resp.Code)
			},
		},
		{
			name: "Empty Schedule Params",
			requestBody: map[string]interface{}{
				"engineModel":    "transcode_nh2.0_1080p_x264",
				"userId":         "test-user",
				"jobId":          "test-job-empty-schedule",
				"analysisMode":   "byScheduleParams",
				"scheduleParams": map[string]interface{}{},
			},
			expectError: false,
			validator: func(resp *v1.AnalyzeJobResponse) {
				// 可能成功也可能失败，取决于业务逻辑
				// 这里主要测试不会崩溃
				assert.NotEmpty(suite.T(), resp.Code)
			},
		},
		{
			name: "Large Input Values",
			requestBody: map[string]interface{}{
				"engineModel":  "transcode_nh2.0_1080p_x264",
				"userId":       "test-user",
				"jobId":        "test-job-large-values",
				"analysisMode": "byScheduleParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType":    "transcode",
							"nhVersion":  "2.0",
							"videoCodec": "x264",
							"duration":   999999.999, // 极大的时长
						},
					},
					"inputs": []map[string]interface{}{
						{
							"width":        7680, // 8K分辨率
							"height":       4320,
							"duration":     999999.999,
							"videoBitrate": 1000000000, // 极高码率
							"size":         999999999999, // 极大文件
						},
					},
				},
			},
			expectError: false,
			validator: func(resp *v1.AnalyzeJobResponse) {
				// 应该能处理大数值而不崩溃
				assert.NotEmpty(suite.T(), resp.Code)
				if resp.Success {
					assert.NotEmpty(suite.T(), resp.QuotaSet)
				}
			},
		},
		{
			name: "Zero and Negative Values",
			requestBody: map[string]interface{}{
				"engineModel":  "transcode_nh2.0_1080p_x264",
				"userId":       "test-user",
				"jobId":        "test-job-zero-negative",
				"analysisMode": "byScheduleParams",
				"scheduleParams": map[string]interface{}{
					"configs": []map[string]interface{}{
						{
							"jobType":      "transcode",
							"nhVersion":    "2.0",
							"videoCodec":   "x264",
							"duration":     0,
							"audioBitrate": -1,
						},
					},
					"inputs": []map[string]interface{}{
						{
							"width":        0,
							"height":       0,
							"duration":     -1,
							"videoBitrate": -1000,
							"size":         0,
						},
					},
				},
			},
			expectError: false,
			validator: func(resp *v1.AnalyzeJobResponse) {
				// 应该能处理零值和负值
				assert.NotEmpty(suite.T(), resp.Code)
			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			jsonBody, err := json.Marshal(tc.requestBody)
			require.NoError(suite.T(), err)

			resp, err := http.Post(
				suite.baseURL+"/job/analysis",
				"application/json",
				bytes.NewBuffer(jsonBody),
			)

			if tc.expectError {
				// 期望HTTP级别的错误
				assert.True(suite.T(), resp.StatusCode >= 400)
			} else {
				require.NoError(suite.T(), err)
				defer resp.Body.Close()

				body, err := io.ReadAll(resp.Body)
				require.NoError(suite.T(), err)

				var analysisResp v1.AnalyzeJobResponse
				err = json.Unmarshal(body, &analysisResp)
				require.NoError(suite.T(), err)

				// 执行自定义验证
				tc.validator(&analysisResp)
			}
		})
	}
}

// TestPerformanceAndLoad 性能和负载测试
func (suite *CurlBasedTestSuite) TestPerformanceAndLoad() {
	// 基本性能测试
	suite.Run("Basic Performance Test", func() {
		requestBody := map[string]interface{}{
			"engineModel":  "transcode_nh2.0_1080p_x264",
			"userId":       "perf-test-user",
			"jobId":        "perf-test-job",
			"analysisMode": "byParseEngineParams",
			"scheduleParams": map[string]interface{}{
				"configs": []map[string]interface{}{
					{
						"jobType":    "transcode",
						"nhVersion":  "2.0",
						"videoCodec": "x264",
					},
				},
				"inputs": []map[string]interface{}{
					{
						"width":  1920,
						"height": 1080,
					},
				},
			},
		}

		// 测量响应时间
		start := time.Now()

		jsonBody, err := json.Marshal(requestBody)
		require.NoError(suite.T(), err)

		resp, err := http.Post(
			suite.baseURL+"/job/analysis",
			"application/json",
			bytes.NewBuffer(jsonBody),
		)
		require.NoError(suite.T(), err)
		defer resp.Body.Close()

		duration := time.Since(start)

		// 验证响应时间在合理范围内（小于5秒）
		assert.Less(suite.T(), duration, 5*time.Second, "Response time should be less than 5 seconds")

		body, err := io.ReadAll(resp.Body)
		require.NoError(suite.T(), err)

		var analysisResp v1.AnalyzeJobResponse
		err = json.Unmarshal(body, &analysisResp)
		require.NoError(suite.T(), err)

		assert.True(suite.T(), analysisResp.Success)

		suite.T().Logf("Performance test completed in %v", duration)
	})

	// 并发测试
	suite.Run("Concurrent Requests Test", func() {
		const numRequests = 10
		const concurrency = 5

		requestBody := map[string]interface{}{
			"engineModel":  "transcode_nh2.0_1080p_x264",
			"userId":       "concurrent-test-user",
			"analysisMode": "byParseEngineParams",
			"scheduleParams": map[string]interface{}{
				"configs": []map[string]interface{}{
					{
						"jobType":    "transcode",
						"videoCodec": "x264",
					},
				},
			},
		}

		// 创建信号量控制并发数
		semaphore := make(chan struct{}, concurrency)
		results := make(chan bool, numRequests)

		start := time.Now()

		// 启动并发请求
		for i := 0; i < numRequests; i++ {
			go func(id int) {
				semaphore <- struct{}{} // 获取信号量
				defer func() { <-semaphore }() // 释放信号量

				// 为每个请求设置唯一的jobId
				reqBody := make(map[string]interface{})
				for k, v := range requestBody {
					reqBody[k] = v
				}
				reqBody["jobId"] = fmt.Sprintf("concurrent-test-job-%d", id)

				jsonBody, err := json.Marshal(reqBody)
				if err != nil {
					results <- false
					return
				}

				resp, err := http.Post(
					suite.baseURL+"/job/analysis",
					"application/json",
					bytes.NewBuffer(jsonBody),
				)
				if err != nil {
					results <- false
					return
				}
				defer resp.Body.Close()

				body, err := io.ReadAll(resp.Body)
				if err != nil {
					results <- false
					return
				}

				var analysisResp v1.AnalyzeJobResponse
				err = json.Unmarshal(body, &analysisResp)
				if err != nil {
					results <- false
					return
				}

				results <- analysisResp.Success
			}(i)
		}

		// 收集结果
		successCount := 0
		for i := 0; i < numRequests; i++ {
			if <-results {
				successCount++
			}
		}

		duration := time.Since(start)

		// 验证所有请求都成功
		assert.Equal(suite.T(), numRequests, successCount, "All concurrent requests should succeed")

		// 验证总时间合理（并发应该比串行快）
		assert.Less(suite.T(), duration, time.Duration(numRequests)*2*time.Second, "Concurrent requests should be faster than serial")

		suite.T().Logf("Concurrent test: %d/%d requests succeeded in %v", successCount, numRequests, duration)
	})
}

// TestCurlBasedSuite 运行基于curl的测试套件
func TestCurlBasedSuite(t *testing.T) {
	// 检查是否跳过集成测试
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping integration tests")
	}

	suite.Run(t, new(CurlBasedTestSuite))
}
