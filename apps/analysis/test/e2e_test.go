package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	kratoshttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
	mysqldriver "gorm.io/driver/mysql"
	"gorm.io/gorm"

	v1 "mps-job-analysis-go/api/job/v1"
	"mps-job-analysis-go/internal/biz"
	"mps-job-analysis-go/internal/data"
	"mps-job-analysis-go/internal/service"
)

// E2ETestSuite 端到端测试套件
type E2ETestSuite struct {
	suite.Suite
	mysqlContainer testcontainers.Container
	httpServer     *kratoshttp.Server
	baseURL        string
	ctx            context.Context
}

// SetupSuite 在所有测试开始前运行
func (suite *E2ETestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 启动 MySQL 容器
	mysqlContainer, err := mysql.Run(suite.ctx,
		"mysql:8.0",
		mysql.WithDatabase("testdb"),
		mysql.WithUsername("testuser"),
		mysql.WithPassword("testpass"),
		mysql.WithScripts(filepath.Join("..", "scripts", "data.sql")),
	)
	require.NoError(suite.T(), err)

	suite.mysqlContainer = mysqlContainer

	// 获取连接信息
	host, err := mysqlContainer.Host(suite.ctx)
	require.NoError(suite.T(), err)

	port, err := mysqlContainer.MappedPort(suite.ctx, "3306")
	require.NoError(suite.T(), err)

	// 创建数据库连接
	dsn := fmt.Sprintf("testuser:testpass@tcp(%s:%s)/testdb?charset=utf8mb4&parseTime=True&loc=Local",
		host, port.Port())

	db, err := gorm.Open(mysqldriver.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)

	// 创建数据访问层 - 使用NewData函数创建
	dataLayer, cleanup, err := data.NewData(nil, log.DefaultLogger, db, nil)
	require.NoError(suite.T(), err)
	defer cleanup()

	// 创建仓库
	jobAnalysisRepo := data.NewJobAnalysisRepo(dataLayer, log.DefaultLogger)
	externalServiceRepo := &E2EMockExternalServiceRepo{}

	// 创建业务逻辑层
	jobAnalysisUsecase := biz.NewJobAnalysisUsecase(jobAnalysisRepo, externalServiceRepo, log.DefaultLogger)

	// 创建服务层
	jobAnalysisService := service.NewJobAnalysisService(jobAnalysisUsecase, log.DefaultLogger)

	// 创建 HTTP 服务器
	suite.httpServer = kratoshttp.NewServer(
		kratoshttp.Address("127.0.0.1:0"), // 使用随机端口
		kratoshttp.Middleware(
			recovery.Recovery(),
			logging.Server(log.DefaultLogger),
		),
	)

	// 注册路由
	v1.RegisterJobAnalysisServiceHTTPServer(suite.httpServer, jobAnalysisService)
	v1.RegisterServiceHealthServiceHTTPServer(suite.httpServer, jobAnalysisService)

	// 启动服务器
	go func() {
		if err := suite.httpServer.Start(suite.ctx); err != nil {
			suite.T().Logf("HTTP server start error: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 获取服务器地址
	endpoint, err := suite.httpServer.Endpoint()
	require.NoError(suite.T(), err)
	suite.baseURL = fmt.Sprintf("http://%s", endpoint.Host)
}

// TearDownSuite 在所有测试结束后运行
func (suite *E2ETestSuite) TearDownSuite() {
	if suite.httpServer != nil {
		suite.httpServer.Stop(suite.ctx)
	}
	if suite.mysqlContainer != nil {
		err := suite.mysqlContainer.Terminate(suite.ctx)
		require.NoError(suite.T(), err)
	}
}

// E2EMockExternalServiceRepo 模拟外部服务仓库
type E2EMockExternalServiceRepo struct{}

func (m *E2EMockExternalServiceRepo) CallDMES(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success": true,
		"quota_set": map[string]int64{
			"cpu":  1500,
			"disk": 2000,
		},
	}, nil
}

func (m *E2EMockExternalServiceRepo) CallWorkerBrain(ctx context.Context, engineModel string, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success": true,
		"quota_set": map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		"expect_cost_time": 300,
	}, nil
}

func (m *E2EMockExternalServiceRepo) CallSLA(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success":             true,
		"sla_finish_delay":    60,
		"sla_queuing_delay":   30,
		"max_migrate_retry":   5,
	}, nil
}

func (m *E2EMockExternalServiceRepo) CallMediaMeta(ctx context.Context, param interface{}) (interface{}, error) {
	return map[string]interface{}{
		"success":  true,
		"duration": 120.5,
		"width":    1920,
		"height":   1080,
		"bitrate":  5000000,
	}, nil
}

// TestHealthCheck 测试健康检查接口
func (suite *E2ETestSuite) TestHealthCheck() {
	// 测试存活性检查
	resp, err := http.Get(suite.baseURL + "/service/liveness")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)

	var healthResp v1.HealthCheckResponse
	err = json.Unmarshal(body, &healthResp)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), "Success", healthResp.Code)
	assert.Equal(suite.T(), "Service is alive", healthResp.Message)

	// 测试就绪性检查
	resp, err = http.Get(suite.baseURL + "/service/readiness")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	body, err = io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)

	err = json.Unmarshal(body, &healthResp)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), "Success", healthResp.Code)
	assert.Equal(suite.T(), "Service is ready", healthResp.Message)
}

// TestAnalyzeJobAPI 测试作业分析接口
func (suite *E2ETestSuite) TestAnalyzeJobAPI() {
	requestBody := map[string]interface{}{
		"engine_model":            "mps-transcode-new",
		"engine_params":           `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"},"audio":{"codec":"aac","bitrate":"128k"}}`,
		"user_id":                 "1207704461481212",
		"job_id":                  "test-job-e2e-123",
		"slice_process":           false,
		"analysis_mode":           "byParseEngineParams",
		"tag":                     "test",
		"max_slice_num":           10,
		"min_slice_duration":      30,
		"use_worker_brain_result": false,
		"invoke_worker_brain":     false,
		"speed_x_range":           []string{"5X"},
		"is_auto_speed_x":         false,
		"pipeline_id":             "test-pipeline-123",
		"request_id":              "test-request-e2e-123",
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	resp, err := http.Post(
		suite.baseURL+"/job/analysis",
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)

	var analysisResp v1.AnalyzeJobResponse
	err = json.Unmarshal(body, &analysisResp)
	require.NoError(suite.T(), err)

	assert.True(suite.T(), analysisResp.Success)
	assert.Equal(suite.T(), "Success", analysisResp.Code)
	assert.Equal(suite.T(), "Analysis completed successfully", analysisResp.Message)
	assert.Equal(suite.T(), v1.ExecuteMode_SINGLE, analysisResp.ExecuteMode)

	// 验证配额设置
	assert.NotEmpty(suite.T(), analysisResp.QuotaSet)
	assert.Contains(suite.T(), analysisResp.QuotaSet, "cpu")
	assert.Contains(suite.T(), analysisResp.QuotaSet, "disk")
	assert.Contains(suite.T(), analysisResp.QuotaSet, "gpu")

	// 验证期望耗时
	assert.NotEmpty(suite.T(), analysisResp.ExpectCostTime)
}

// TestAnalyzeJobWithWorkerBrain 测试使用 WorkerBrain 的作业分析
func (suite *E2ETestSuite) TestAnalyzeJobWithWorkerBrain() {
	requestBody := map[string]interface{}{
		"engine_model":            "mps-transcode-new",
		"engine_params":           `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"}}`,
		"user_id":                 "test-user-123",
		"job_id":                  "test-job-worker-brain-e2e",
		"analysis_mode":           "byParseEngineParams",
		"invoke_worker_brain":     true,
		"use_worker_brain_result": true,
		"tag":                     "test",
		"pipeline_id":             "test-pipeline",
		"request_id":              "test-request",
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	resp, err := http.Post(
		suite.baseURL+"/job/analysis",
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)

	var analysisResp v1.AnalyzeJobResponse
	err = json.Unmarshal(body, &analysisResp)
	require.NoError(suite.T(), err)

	assert.True(suite.T(), analysisResp.Success)

	// 验证是否使用了 MockExternalServiceRepo 返回的值
	assert.Equal(suite.T(), int64(2000), analysisResp.QuotaSet["cpu"])
	assert.Equal(suite.T(), int64(100000), analysisResp.QuotaSet["gpu"])
}

// TestReportResultAPI 测试结果报告接口
func (suite *E2ETestSuite) TestReportResultAPI() {
	requestBody := map[string]interface{}{
		"job_id": "test-job-report-e2e",
		"alloc_quota_set": map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		"max_quota_set": map[string]int64{
			"cpu": 2500,
			"gpu": 120000,
		},
		"avg_quota_set": map[string]int64{
			"cpu": 1800,
			"gpu": 90000,
		},
		"engine_model":    "mps-transcode-new",
		"engine_params":   `{"format":"mp4","video":{"codec":"h264"}}`,
		"expect_cost_time": 300,
		"real_cost_time":   280,
		"pipeline_id":      "test-pipeline",
		"tag":              "test",
		"request_id":       "test-request",
		"product":          "mps",
		"user_id":          "test-user-123",
		"env":              "test",
		"station":          "test-station",
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	resp, err := http.Post(
		suite.baseURL+"/job/reportResult",
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)

	var reportResp v1.ReportResultResponse
	err = json.Unmarshal(body, &reportResp)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), "Success", reportResp.Code)
	assert.Equal(suite.T(), "Result reported successfully", reportResp.Message)
}

// TestInvalidRequests 测试无效请求
func (suite *E2ETestSuite) TestInvalidRequests() {
	testCases := []struct {
		name        string
		requestBody map[string]interface{}
		expectError bool
	}{
		{
			name: "missing engine_model",
			requestBody: map[string]interface{}{
				"engine_params": `{"format":"mp4"}`,
				"user_id":       "test-user",
				"job_id":        "test-job",
				"analysis_mode": "byParseEngineParams",
			},
			expectError: true,
		},
		{
			name: "missing engine_params",
			requestBody: map[string]interface{}{
				"engine_model":  "mps-transcode-new",
				"user_id":       "test-user",
				"job_id":        "test-job",
				"analysis_mode": "byParseEngineParams",
			},
			expectError: true,
		},
		{
			name: "invalid JSON",
			requestBody: map[string]interface{}{
				"engine_model":  "mps-transcode-new",
				"engine_params": `{"format":"mp4"`, // 无效的 JSON
				"user_id":       "test-user",
				"job_id":        "test-job",
				"analysis_mode": "byParseEngineParams",
			},
			expectError: false, // 服务应该处理这种情况并返回错误响应
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			jsonBody, err := json.Marshal(tc.requestBody)
			require.NoError(suite.T(), err)

			resp, err := http.Post(
				suite.baseURL+"/job/analysis",
				"application/json",
				bytes.NewBuffer(jsonBody),
			)

			if tc.expectError {
				// 对于缺少必需字段的请求，应该返回 400 错误
				assert.True(suite.T(), resp.StatusCode >= 400)
			} else {
				require.NoError(suite.T(), err)
				defer resp.Body.Close()

				body, err := io.ReadAll(resp.Body)
				require.NoError(suite.T(), err)

				var analysisResp v1.AnalyzeJobResponse
				err = json.Unmarshal(body, &analysisResp)
				require.NoError(suite.T(), err)

				// 对于无效的 JSON，服务应该返回失败响应
				assert.False(suite.T(), analysisResp.Success)
			}
		})
	}
}

// TestE2ESuite 运行端到端测试套件
func TestE2ESuite(t *testing.T) {
	// 检查是否在 CI 环境中或者有 Docker
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping integration tests")
	}

	suite.Run(t, new(E2ETestSuite))
}
