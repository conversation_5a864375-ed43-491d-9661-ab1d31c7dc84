package main

import (
	"flag"
	"os"

	"github.com/cinience/animus"
	"github.com/cinience/animus/config"
	"github.com/cinience/animus/contribs/registry/nacos"
	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"

	"mps-job-analysis-go/internal/conf"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = "mpp-analysis"
	// Version is the version of the compiled software.
	Version string = "v1.0.0"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&config.FlagConf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, reg registry.Registrar) *animus.App {
	return animus.New(
		animus.ID(id),
		animus.Name(Name),
		animus.Version(Version),
		animus.Metadata(map[string]string{}),
		animus.Logger(logger),
		animus.Server(
			gs,
			hs,
		),
		animus.Registrar([]registry.Registrar{reg}),
	)
}

func NewRegister(regConf *conf.Registry) registry.Registrar {
	if regConf == nil {
		return nil
	}
	r := nacos.NewWithURL(regConf.GetEndpoint())
	if r == nil {
		return r
	}

	return r
}

func main() {
	flag.Parse()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)

	var bc conf.Bootstrap
	if err := config.NewConfig(&bc); err != nil {
		panic(err)
	}

	reg := NewRegister(bc.App.Registry)

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.ExternalServices, logger, reg)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
