//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"mps-job-analysis-go/internal/biz"
	"mps-job-analysis-go/internal/conf"
	"mps-job-analysis-go/internal/data"
	"mps-job-analysis-go/internal/server"
	"mps-job-analysis-go/internal/service"

	"github.com/cinience/animus"
	"github.com/cinience/animus/registry"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, *conf.ExternalServices, log.Logger, registry.Registrar) (*animus.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
