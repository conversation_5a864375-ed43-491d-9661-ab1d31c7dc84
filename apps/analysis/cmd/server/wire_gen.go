// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/cinience/animus"
	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
	"mps-job-analysis-go/internal/biz"
	"mps-job-analysis-go/internal/conf"
	"mps-job-analysis-go/internal/data"
	"mps-job-analysis-go/internal/server"
	"mps-job-analysis-go/internal/service"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, externalServices *conf.ExternalServices, logger log.Logger, registrar registry.Registrar) (*animus.App, func(), error) {
	db := data.NewDB(confData, logger)
	client := data.NewRedis(confData, logger)
	dataData, cleanup, err := data.NewData(confData, logger, db, client)
	if err != nil {
		return nil, nil, err
	}
	jobAnalysisRepo := data.NewJobAnalysisRepo(dataData, logger)
	externalServiceRepo := data.NewExternalServiceRepo(externalServices, logger)
	jobAnalysisUsecase := biz.NewJobAnalysisUsecase(jobAnalysisRepo, externalServiceRepo, logger)
	jobAnalysisService := service.NewJobAnalysisService(jobAnalysisUsecase, logger)
	grpcServer := server.NewGRPCServer(confServer, jobAnalysisService, logger)
	httpServer := server.NewHTTPServer(confServer, jobAnalysisService, logger)
	app := newApp(logger, grpcServer, httpServer, registrar)
	return app, func() {
		cleanup()
	}, nil
}
