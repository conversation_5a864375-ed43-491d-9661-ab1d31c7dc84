// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: job/v1/job.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 执行模式枚举
type ExecuteMode int32

const (
	ExecuteMode_PROBE_FIRST ExecuteMode = 0
	ExecuteMode_SINGLE      ExecuteMode = 1
	ExecuteMode_DAG         ExecuteMode = 2
)

// Enum value maps for ExecuteMode.
var (
	ExecuteMode_name = map[int32]string{
		0: "PROBE_FIRST",
		1: "SINGLE",
		2: "DAG",
	}
	ExecuteMode_value = map[string]int32{
		"PROBE_FIRST": 0,
		"SINGLE":      1,
		"DAG":         2,
	}
)

func (x ExecuteMode) Enum() *ExecuteMode {
	p := new(ExecuteMode)
	*p = x
	return p
}

func (x ExecuteMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecuteMode) Descriptor() protoreflect.EnumDescriptor {
	return file_job_v1_job_proto_enumTypes[0].Descriptor()
}

func (ExecuteMode) Type() protoreflect.EnumType {
	return &file_job_v1_job_proto_enumTypes[0]
}

func (x ExecuteMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecuteMode.Descriptor instead.
func (ExecuteMode) EnumDescriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{0}
}

// 作业分析请求
type AnalyzeJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EngineModel          string                 `protobuf:"bytes,1,opt,name=engine_model,json=engineModel,proto3" json:"engine_model,omitempty"`
	EngineParams         string                 `protobuf:"bytes,2,opt,name=engine_params,json=engineParams,proto3" json:"engine_params,omitempty"`
	UserId               string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	JobId                string                 `protobuf:"bytes,4,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	SliceProcess         bool                   `protobuf:"varint,5,opt,name=slice_process,json=sliceProcess,proto3" json:"slice_process,omitempty"`
	AnalysisMode         string                 `protobuf:"bytes,6,opt,name=analysis_mode,json=analysisMode,proto3" json:"analysis_mode,omitempty"`
	ScheduleParams       *ScheduleParams        `protobuf:"bytes,7,opt,name=schedule_params,json=scheduleParams,proto3" json:"schedule_params,omitempty"`
	Tag                  string                 `protobuf:"bytes,8,opt,name=tag,proto3" json:"tag,omitempty"`
	CreateTime           *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	MaxSliceNum          int32                  `protobuf:"varint,10,opt,name=max_slice_num,json=maxSliceNum,proto3" json:"max_slice_num,omitempty"`
	MinSliceDuration     int32                  `protobuf:"varint,11,opt,name=min_slice_duration,json=minSliceDuration,proto3" json:"min_slice_duration,omitempty"`
	UseWorkerBrainResult bool                   `protobuf:"varint,12,opt,name=use_worker_brain_result,json=useWorkerBrainResult,proto3" json:"use_worker_brain_result,omitempty"`
	InvokeWorkerBrain    bool                   `protobuf:"varint,13,opt,name=invoke_worker_brain,json=invokeWorkerBrain,proto3" json:"invoke_worker_brain,omitempty"`
	SpeedXRange          []string               `protobuf:"bytes,14,rep,name=speed_x_range,json=speedXRange,proto3" json:"speed_x_range,omitempty"`
	IsAutoSpeedX         bool                   `protobuf:"varint,15,opt,name=is_auto_speed_x,json=isAutoSpeedX,proto3" json:"is_auto_speed_x,omitempty"`
	Trace                string                 `protobuf:"bytes,16,opt,name=trace,proto3" json:"trace,omitempty"` // JSON string
	PipelineId           string                 `protobuf:"bytes,17,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	RequestId            string                 `protobuf:"bytes,18,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *AnalyzeJobRequest) Reset() {
	*x = AnalyzeJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyzeJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeJobRequest) ProtoMessage() {}

func (x *AnalyzeJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeJobRequest.ProtoReflect.Descriptor instead.
func (*AnalyzeJobRequest) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{0}
}

func (x *AnalyzeJobRequest) GetEngineModel() string {
	if x != nil {
		return x.EngineModel
	}
	return ""
}

func (x *AnalyzeJobRequest) GetEngineParams() string {
	if x != nil {
		return x.EngineParams
	}
	return ""
}

func (x *AnalyzeJobRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AnalyzeJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *AnalyzeJobRequest) GetSliceProcess() bool {
	if x != nil {
		return x.SliceProcess
	}
	return false
}

func (x *AnalyzeJobRequest) GetAnalysisMode() string {
	if x != nil {
		return x.AnalysisMode
	}
	return ""
}

func (x *AnalyzeJobRequest) GetScheduleParams() *ScheduleParams {
	if x != nil {
		return x.ScheduleParams
	}
	return nil
}

func (x *AnalyzeJobRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *AnalyzeJobRequest) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AnalyzeJobRequest) GetMaxSliceNum() int32 {
	if x != nil {
		return x.MaxSliceNum
	}
	return 0
}

func (x *AnalyzeJobRequest) GetMinSliceDuration() int32 {
	if x != nil {
		return x.MinSliceDuration
	}
	return 0
}

func (x *AnalyzeJobRequest) GetUseWorkerBrainResult() bool {
	if x != nil {
		return x.UseWorkerBrainResult
	}
	return false
}

func (x *AnalyzeJobRequest) GetInvokeWorkerBrain() bool {
	if x != nil {
		return x.InvokeWorkerBrain
	}
	return false
}

func (x *AnalyzeJobRequest) GetSpeedXRange() []string {
	if x != nil {
		return x.SpeedXRange
	}
	return nil
}

func (x *AnalyzeJobRequest) GetIsAutoSpeedX() bool {
	if x != nil {
		return x.IsAutoSpeedX
	}
	return false
}

func (x *AnalyzeJobRequest) GetTrace() string {
	if x != nil {
		return x.Trace
	}
	return ""
}

func (x *AnalyzeJobRequest) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *AnalyzeJobRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// 作业分析响应
type AnalyzeJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success                      bool                      `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Code                         string                    `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Message                      string                    `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ExecuteMode                  ExecuteMode               `protobuf:"varint,4,opt,name=execute_mode,json=executeMode,proto3,enum=api.job.v1.ExecuteMode" json:"execute_mode,omitempty"`
	ExpectCostTime               int64                     `protobuf:"varint,5,opt,name=expect_cost_time,json=expectCostTime,proto3" json:"expect_cost_time,omitempty"`
	QuotaSet                     map[string]int64          `protobuf:"bytes,6,rep,name=quota_set,json=quotaSet,proto3" json:"quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	OriginQuotaSet               map[string]int64          `protobuf:"bytes,7,rep,name=origin_quota_set,json=originQuotaSet,proto3" json:"origin_quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	SlaFinishDelay               int64                     `protobuf:"varint,8,opt,name=sla_finish_delay,json=slaFinishDelay,proto3" json:"sla_finish_delay,omitempty"`
	SlaQueuingDelay              int64                     `protobuf:"varint,9,opt,name=sla_queuing_delay,json=slaQueuingDelay,proto3" json:"sla_queuing_delay,omitempty"`
	MaxMigrateRetry              int32                     `protobuf:"varint,10,opt,name=max_migrate_retry,json=maxMigrateRetry,proto3" json:"max_migrate_retry,omitempty"`
	MigrateDiscardQuotaThreshold map[string]int64          `protobuf:"bytes,11,rep,name=migrate_discard_quota_threshold,json=migrateDiscardQuotaThreshold,proto3" json:"migrate_discard_quota_threshold,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Graph                        *DagJobGraph              `protobuf:"bytes,12,opt,name=graph,proto3" json:"graph,omitempty"`
	GraphMap                     map[string]*DagJobGraph   `protobuf:"bytes,13,rep,name=graph_map,json=graphMap,proto3" json:"graph_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SpeedXMessage                map[string]*SpeedXMessage `protobuf:"bytes,14,rep,name=speed_x_message,json=speedXMessage,proto3" json:"speed_x_message,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AnalyzeJobResponse) Reset() {
	*x = AnalyzeJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyzeJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeJobResponse) ProtoMessage() {}

func (x *AnalyzeJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeJobResponse.ProtoReflect.Descriptor instead.
func (*AnalyzeJobResponse) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{1}
}

func (x *AnalyzeJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AnalyzeJobResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AnalyzeJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AnalyzeJobResponse) GetExecuteMode() ExecuteMode {
	if x != nil {
		return x.ExecuteMode
	}
	return ExecuteMode_PROBE_FIRST
}

func (x *AnalyzeJobResponse) GetExpectCostTime() int64 {
	if x != nil {
		return x.ExpectCostTime
	}
	return 0
}

func (x *AnalyzeJobResponse) GetQuotaSet() map[string]int64 {
	if x != nil {
		return x.QuotaSet
	}
	return nil
}

func (x *AnalyzeJobResponse) GetOriginQuotaSet() map[string]int64 {
	if x != nil {
		return x.OriginQuotaSet
	}
	return nil
}

func (x *AnalyzeJobResponse) GetSlaFinishDelay() int64 {
	if x != nil {
		return x.SlaFinishDelay
	}
	return 0
}

func (x *AnalyzeJobResponse) GetSlaQueuingDelay() int64 {
	if x != nil {
		return x.SlaQueuingDelay
	}
	return 0
}

func (x *AnalyzeJobResponse) GetMaxMigrateRetry() int32 {
	if x != nil {
		return x.MaxMigrateRetry
	}
	return 0
}

func (x *AnalyzeJobResponse) GetMigrateDiscardQuotaThreshold() map[string]int64 {
	if x != nil {
		return x.MigrateDiscardQuotaThreshold
	}
	return nil
}

func (x *AnalyzeJobResponse) GetGraph() *DagJobGraph {
	if x != nil {
		return x.Graph
	}
	return nil
}

func (x *AnalyzeJobResponse) GetGraphMap() map[string]*DagJobGraph {
	if x != nil {
		return x.GraphMap
	}
	return nil
}

func (x *AnalyzeJobResponse) GetSpeedXMessage() map[string]*SpeedXMessage {
	if x != nil {
		return x.SpeedXMessage
	}
	return nil
}

// 结果报告请求
type ReportResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId          string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	AllocQuotaSet  map[string]int64       `protobuf:"bytes,2,rep,name=alloc_quota_set,json=allocQuotaSet,proto3" json:"alloc_quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MaxQuotaSet    map[string]int64       `protobuf:"bytes,3,rep,name=max_quota_set,json=maxQuotaSet,proto3" json:"max_quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	AvgQuotaSet    map[string]int64       `protobuf:"bytes,4,rep,name=avg_quota_set,json=avgQuotaSet,proto3" json:"avg_quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	EngineModel    string                 `protobuf:"bytes,5,opt,name=engine_model,json=engineModel,proto3" json:"engine_model,omitempty"`
	EngineParams   string                 `protobuf:"bytes,6,opt,name=engine_params,json=engineParams,proto3" json:"engine_params,omitempty"`
	ExpectCostTime int64                  `protobuf:"varint,7,opt,name=expect_cost_time,json=expectCostTime,proto3" json:"expect_cost_time,omitempty"`
	RealCostTime   int64                  `protobuf:"varint,8,opt,name=real_cost_time,json=realCostTime,proto3" json:"real_cost_time,omitempty"`
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	SubmitTime     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=submit_time,json=submitTime,proto3" json:"submit_time,omitempty"`
	Trace          string                 `protobuf:"bytes,11,opt,name=trace,proto3" json:"trace,omitempty"` // JSON string
	PipelineId     string                 `protobuf:"bytes,12,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	Tag            string                 `protobuf:"bytes,13,opt,name=tag,proto3" json:"tag,omitempty"`
	RequestId      string                 `protobuf:"bytes,14,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Product        string                 `protobuf:"bytes,15,opt,name=product,proto3" json:"product,omitempty"`
	ResultData     string                 `protobuf:"bytes,16,opt,name=result_data,json=resultData,proto3" json:"result_data,omitempty"` // JSON string
	UserId         string                 `protobuf:"bytes,17,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Env            string                 `protobuf:"bytes,18,opt,name=env,proto3" json:"env,omitempty"`
	Station        string                 `protobuf:"bytes,19,opt,name=station,proto3" json:"station,omitempty"`
}

func (x *ReportResultRequest) Reset() {
	*x = ReportResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportResultRequest) ProtoMessage() {}

func (x *ReportResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportResultRequest.ProtoReflect.Descriptor instead.
func (*ReportResultRequest) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{2}
}

func (x *ReportResultRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *ReportResultRequest) GetAllocQuotaSet() map[string]int64 {
	if x != nil {
		return x.AllocQuotaSet
	}
	return nil
}

func (x *ReportResultRequest) GetMaxQuotaSet() map[string]int64 {
	if x != nil {
		return x.MaxQuotaSet
	}
	return nil
}

func (x *ReportResultRequest) GetAvgQuotaSet() map[string]int64 {
	if x != nil {
		return x.AvgQuotaSet
	}
	return nil
}

func (x *ReportResultRequest) GetEngineModel() string {
	if x != nil {
		return x.EngineModel
	}
	return ""
}

func (x *ReportResultRequest) GetEngineParams() string {
	if x != nil {
		return x.EngineParams
	}
	return ""
}

func (x *ReportResultRequest) GetExpectCostTime() int64 {
	if x != nil {
		return x.ExpectCostTime
	}
	return 0
}

func (x *ReportResultRequest) GetRealCostTime() int64 {
	if x != nil {
		return x.RealCostTime
	}
	return 0
}

func (x *ReportResultRequest) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ReportResultRequest) GetSubmitTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SubmitTime
	}
	return nil
}

func (x *ReportResultRequest) GetTrace() string {
	if x != nil {
		return x.Trace
	}
	return ""
}

func (x *ReportResultRequest) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *ReportResultRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ReportResultRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ReportResultRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *ReportResultRequest) GetResultData() string {
	if x != nil {
		return x.ResultData
	}
	return ""
}

func (x *ReportResultRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ReportResultRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *ReportResultRequest) GetStation() string {
	if x != nil {
		return x.Station
	}
	return ""
}

// 结果报告响应
type ReportResultResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ReportResultResponse) Reset() {
	*x = ReportResultResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportResultResponse) ProtoMessage() {}

func (x *ReportResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportResultResponse.ProtoReflect.Descriptor instead.
func (*ReportResultResponse) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{3}
}

func (x *ReportResultResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ReportResultResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 健康检查请求
type HealthCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{4}
}

// 健康检查响应
type HealthCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{5}
}

func (x *HealthCheckResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *HealthCheckResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 调度参数
type ScheduleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineId     string           `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	QuotaSet       map[string]int64 `protobuf:"bytes,2,rep,name=quota_set,json=quotaSet,proto3" json:"quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Priority       int32            `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	ExpectCostTime int32            `protobuf:"varint,4,opt,name=expect_cost_time,json=expectCostTime,proto3" json:"expect_cost_time,omitempty"`
	Inputs         []*Input         `protobuf:"bytes,5,rep,name=inputs,proto3" json:"inputs,omitempty"`
	Configs        []*Config        `protobuf:"bytes,6,rep,name=configs,proto3" json:"configs,omitempty"`
	ParallelNum    int32            `protobuf:"varint,7,opt,name=parallel_num,json=parallelNum,proto3" json:"parallel_num,omitempty"`
	SlaLevel       string           `protobuf:"bytes,8,opt,name=sla_level,json=slaLevel,proto3" json:"sla_level,omitempty"`
	SliceNum       int32            `protobuf:"varint,9,opt,name=slice_num,json=sliceNum,proto3" json:"slice_num,omitempty"`
	SpeedXRange    []string         `protobuf:"bytes,10,rep,name=speed_x_range,json=speedXRange,proto3" json:"speed_x_range,omitempty"`
}

func (x *ScheduleParams) Reset() {
	*x = ScheduleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleParams) ProtoMessage() {}

func (x *ScheduleParams) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleParams.ProtoReflect.Descriptor instead.
func (*ScheduleParams) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{6}
}

func (x *ScheduleParams) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *ScheduleParams) GetQuotaSet() map[string]int64 {
	if x != nil {
		return x.QuotaSet
	}
	return nil
}

func (x *ScheduleParams) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *ScheduleParams) GetExpectCostTime() int32 {
	if x != nil {
		return x.ExpectCostTime
	}
	return 0
}

func (x *ScheduleParams) GetInputs() []*Input {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *ScheduleParams) GetConfigs() []*Config {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *ScheduleParams) GetParallelNum() int32 {
	if x != nil {
		return x.ParallelNum
	}
	return 0
}

func (x *ScheduleParams) GetSlaLevel() string {
	if x != nil {
		return x.SlaLevel
	}
	return ""
}

func (x *ScheduleParams) GetSliceNum() int32 {
	if x != nil {
		return x.SliceNum
	}
	return 0
}

func (x *ScheduleParams) GetSpeedXRange() []string {
	if x != nil {
		return x.SpeedXRange
	}
	return nil
}

// 输入参数
type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration     float64 `protobuf:"fixed64,1,opt,name=duration,proto3" json:"duration,omitempty"`
	VideoBitrate float64 `protobuf:"fixed64,2,opt,name=video_bitrate,json=videoBitrate,proto3" json:"video_bitrate,omitempty"`
	AvgFps       float64 `protobuf:"fixed64,3,opt,name=avg_fps,json=avgFps,proto3" json:"avg_fps,omitempty"`
	Size         int64   `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	Format       string  `protobuf:"bytes,5,opt,name=format,proto3" json:"format,omitempty"`
	Fps          float64 `protobuf:"fixed64,6,opt,name=fps,proto3" json:"fps,omitempty"`
	Width        int32   `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`
	AudioCodec   string  `protobuf:"bytes,8,opt,name=audio_codec,json=audioCodec,proto3" json:"audio_codec,omitempty"`
	AudioBitrate float64 `protobuf:"fixed64,9,opt,name=audio_bitrate,json=audioBitrate,proto3" json:"audio_bitrate,omitempty"`
	Height       int32   `protobuf:"varint,10,opt,name=height,proto3" json:"height,omitempty"`
	VideoCodec   string  `protobuf:"bytes,11,opt,name=video_codec,json=videoCodec,proto3" json:"video_codec,omitempty"`
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{7}
}

func (x *Input) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Input) GetVideoBitrate() float64 {
	if x != nil {
		return x.VideoBitrate
	}
	return 0
}

func (x *Input) GetAvgFps() float64 {
	if x != nil {
		return x.AvgFps
	}
	return 0
}

func (x *Input) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Input) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Input) GetFps() float64 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *Input) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Input) GetAudioCodec() string {
	if x != nil {
		return x.AudioCodec
	}
	return ""
}

func (x *Input) GetAudioBitrate() float64 {
	if x != nil {
		return x.AudioBitrate
	}
	return 0
}

func (x *Input) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Input) GetVideoCodec() string {
	if x != nil {
		return x.VideoCodec
	}
	return ""
}

// 配置参数
type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration      float64           `protobuf:"fixed64,1,opt,name=duration,proto3" json:"duration,omitempty"`
	NhVersion     string            `protobuf:"bytes,2,opt,name=nh_version,json=nhVersion,proto3" json:"nh_version,omitempty"`
	Format        string            `protobuf:"bytes,3,opt,name=format,proto3" json:"format,omitempty"`
	Fps           float64           `protobuf:"fixed64,4,opt,name=fps,proto3" json:"fps,omitempty"`
	Id            string            `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	AudioCodec    string            `protobuf:"bytes,6,opt,name=audio_codec,json=audioCodec,proto3" json:"audio_codec,omitempty"`
	JobType       string            `protobuf:"bytes,7,opt,name=job_type,json=jobType,proto3" json:"job_type,omitempty"`
	TemplateId    string            `protobuf:"bytes,8,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	AudioBitrate  float64           `protobuf:"fixed64,9,opt,name=audio_bitrate,json=audioBitrate,proto3" json:"audio_bitrate,omitempty"`
	VideoCodec    string            `protobuf:"bytes,10,opt,name=video_codec,json=videoCodec,proto3" json:"video_codec,omitempty"`
	AudioOnly     bool              `protobuf:"varint,11,opt,name=audio_only,json=audioOnly,proto3" json:"audio_only,omitempty"`
	Extend        map[string]string `protobuf:"bytes,12,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	GpuRestore    string            `protobuf:"bytes,13,opt,name=gpu_restore,json=gpuRestore,proto3" json:"gpu_restore,omitempty"`
	ByWorkerBrain bool              `protobuf:"varint,14,opt,name=by_worker_brain,json=byWorkerBrain,proto3" json:"by_worker_brain,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{8}
}

func (x *Config) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Config) GetNhVersion() string {
	if x != nil {
		return x.NhVersion
	}
	return ""
}

func (x *Config) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Config) GetFps() float64 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *Config) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Config) GetAudioCodec() string {
	if x != nil {
		return x.AudioCodec
	}
	return ""
}

func (x *Config) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *Config) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Config) GetAudioBitrate() float64 {
	if x != nil {
		return x.AudioBitrate
	}
	return 0
}

func (x *Config) GetVideoCodec() string {
	if x != nil {
		return x.VideoCodec
	}
	return ""
}

func (x *Config) GetAudioOnly() bool {
	if x != nil {
		return x.AudioOnly
	}
	return false
}

func (x *Config) GetExtend() map[string]string {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *Config) GetGpuRestore() string {
	if x != nil {
		return x.GpuRestore
	}
	return ""
}

func (x *Config) GetByWorkerBrain() bool {
	if x != nil {
		return x.ByWorkerBrain
	}
	return false
}

// DAG作业图
type DagJobGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobVertexs []*JobVertex `protobuf:"bytes,1,rep,name=job_vertexs,json=jobVertexs,proto3" json:"job_vertexs,omitempty"`
	JobEdges   []*JobEdge   `protobuf:"bytes,2,rep,name=job_edges,json=jobEdges,proto3" json:"job_edges,omitempty"`
}

func (x *DagJobGraph) Reset() {
	*x = DagJobGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DagJobGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DagJobGraph) ProtoMessage() {}

func (x *DagJobGraph) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DagJobGraph.ProtoReflect.Descriptor instead.
func (*DagJobGraph) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{9}
}

func (x *DagJobGraph) GetJobVertexs() []*JobVertex {
	if x != nil {
		return x.JobVertexs
	}
	return nil
}

func (x *DagJobGraph) GetJobEdges() []*JobEdge {
	if x != nil {
		return x.JobEdges
	}
	return nil
}

// 作业顶点
type JobVertex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	EngineModel     string               `protobuf:"bytes,2,opt,name=engine_model,json=engineModel,proto3" json:"engine_model,omitempty"`
	EngineParams    string               `protobuf:"bytes,3,opt,name=engine_params,json=engineParams,proto3" json:"engine_params,omitempty"`
	ResourceRequest *TaskResourceRequest `protobuf:"bytes,4,opt,name=resource_request,json=resourceRequest,proto3" json:"resource_request,omitempty"`
	ScheduleParams  *ScheduleParams      `protobuf:"bytes,5,opt,name=schedule_params,json=scheduleParams,proto3" json:"schedule_params,omitempty"`
	MaxMigrateRetry int32                `protobuf:"varint,6,opt,name=max_migrate_retry,json=maxMigrateRetry,proto3" json:"max_migrate_retry,omitempty"`
}

func (x *JobVertex) Reset() {
	*x = JobVertex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobVertex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobVertex) ProtoMessage() {}

func (x *JobVertex) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobVertex.ProtoReflect.Descriptor instead.
func (*JobVertex) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{10}
}

func (x *JobVertex) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobVertex) GetEngineModel() string {
	if x != nil {
		return x.EngineModel
	}
	return ""
}

func (x *JobVertex) GetEngineParams() string {
	if x != nil {
		return x.EngineParams
	}
	return ""
}

func (x *JobVertex) GetResourceRequest() *TaskResourceRequest {
	if x != nil {
		return x.ResourceRequest
	}
	return nil
}

func (x *JobVertex) GetScheduleParams() *ScheduleParams {
	if x != nil {
		return x.ScheduleParams
	}
	return nil
}

func (x *JobVertex) GetMaxMigrateRetry() int32 {
	if x != nil {
		return x.MaxMigrateRetry
	}
	return 0
}

// 作业边
type JobEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	From       string `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To         string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	Mode       string `protobuf:"bytes,4,opt,name=mode,proto3" json:"mode,omitempty"`
	Status     string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	FromStatus string `protobuf:"bytes,6,opt,name=from_status,json=fromStatus,proto3" json:"from_status,omitempty"`
}

func (x *JobEdge) Reset() {
	*x = JobEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobEdge) ProtoMessage() {}

func (x *JobEdge) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobEdge.ProtoReflect.Descriptor instead.
func (*JobEdge) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{11}
}

func (x *JobEdge) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobEdge) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *JobEdge) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *JobEdge) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *JobEdge) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *JobEdge) GetFromStatus() string {
	if x != nil {
		return x.FromStatus
	}
	return ""
}

// 任务资源请求
type TaskResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpectCostTime int64            `protobuf:"varint,1,opt,name=expect_cost_time,json=expectCostTime,proto3" json:"expect_cost_time,omitempty"`
	QuotaSet       map[string]int64 `protobuf:"bytes,2,rep,name=quota_set,json=quotaSet,proto3" json:"quota_set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *TaskResourceRequest) Reset() {
	*x = TaskResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskResourceRequest) ProtoMessage() {}

func (x *TaskResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskResourceRequest.ProtoReflect.Descriptor instead.
func (*TaskResourceRequest) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{12}
}

func (x *TaskResourceRequest) GetExpectCostTime() int64 {
	if x != nil {
		return x.ExpectCostTime
	}
	return 0
}

func (x *TaskResourceRequest) GetQuotaSet() map[string]int64 {
	if x != nil {
		return x.QuotaSet
	}
	return nil
}

// 倍速消息
type SpeedXMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SpeedXMessage) Reset() {
	*x = SpeedXMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_v1_job_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedXMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedXMessage) ProtoMessage() {}

func (x *SpeedXMessage) ProtoReflect() protoreflect.Message {
	mi := &file_job_v1_job_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedXMessage.ProtoReflect.Descriptor instead.
func (*SpeedXMessage) Descriptor() ([]byte, []int) {
	return file_job_v1_job_proto_rawDescGZIP(), []int{13}
}

func (x *SpeedXMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SpeedXMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_job_v1_job_proto protoreflect.FileDescriptor

var file_job_v1_job_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6a, 0x6f, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x05, 0x0a, 0x11, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x7a, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x0c,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6c, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x6c,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d,
	0x61, 0x78, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x69,
	0x6e, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x53, 0x6c, 0x69, 0x63, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x17, 0x75, 0x73, 0x65, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x5f, 0x62, 0x72, 0x61, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x75, 0x73, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x42, 0x72, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x5f, 0x62, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x6e,
	0x76, 0x6f, 0x6b, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x42, 0x72, 0x61, 0x69, 0x6e, 0x12,
	0x22, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x65, 0x64, 0x58, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x41, 0x75, 0x74, 0x6f, 0x53, 0x70, 0x65, 0x65, 0x64, 0x58, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x22, 0xd0, 0x09, 0x0a, 0x12, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x3a, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73,
	0x65, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a,
	0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74,
	0x12, 0x5c, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61,
	0x5f, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x28,
	0x0a, 0x10, 0x73, 0x6c, 0x61, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x6c, 0x61, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6c, 0x61, 0x5f,
	0x71, 0x75, 0x65, 0x75, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x6c, 0x61, 0x51, 0x75, 0x65, 0x75, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x6c, 0x61, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x69, 0x67, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x6d, 0x61, 0x78, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x12, 0x87, 0x01, 0x0a, 0x1f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x69, 0x73,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1c, 0x6d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2d, 0x0a, 0x05, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x67, 0x4a, 0x6f, 0x62, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x52, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x49, 0x0a, 0x09, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a,
	0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x4d, 0x61, 0x70, 0x12, 0x59, 0x0a, 0x0f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x78, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x7a, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x58, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x58, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x3b, 0x0a, 0x0d, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13,
	0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x4f, 0x0a, 0x21, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x61, 0x72,
	0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x54, 0x0a, 0x0d, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x67, 0x4a, 0x6f, 0x62, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5b, 0x0a, 0x12, 0x53, 0x70, 0x65, 0x65, 0x64, 0x58,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x58, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xf9, 0x07, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x06, 0x6a,
	0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0f, 0x61,
	0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x54, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x71,
	0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4d, 0x61, 0x78, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x54, 0x0a,
	0x0d, 0x61, 0x76, 0x67, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x76, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x76, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72,
	0x65, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x6e, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x40, 0x0a,
	0x12, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x78, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x3e, 0x0a, 0x10, 0x41, 0x76, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x44, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x43, 0x0a, 0x13, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xd5, 0x03, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63,
	0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x6c, 0x61, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x6c, 0x61, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6c,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73,
	0x6c, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x5f, 0x78, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x58, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x3b, 0x0a, 0x0d, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb4, 0x02, 0x0a, 0x05, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x69, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x76, 0x67, 0x46, 0x70, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x70, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x66, 0x70, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x64, 0x65,
	0x63, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x62, 0x69, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x42,
	0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x63, 0x22,
	0xfb, 0x03, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x68, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x68, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x66, 0x70, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x66, 0x70, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x42, 0x69, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x64,
	0x65, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x6f, 0x6e, 0x6c, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x4f, 0x6e, 0x6c,
	0x79, 0x12, 0x36, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x70, 0x75,
	0x5f, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x67, 0x70, 0x75, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x79,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x5f, 0x62, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x62, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x42, 0x72, 0x61,
	0x69, 0x6e, 0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x77, 0x0a,
	0x0b, 0x44, 0x61, 0x67, 0x4a, 0x6f, 0x62, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x36, 0x0a, 0x0b,
	0x6a, 0x6f, 0x62, 0x5f, 0x76, 0x65, 0x72, 0x74, 0x65, 0x78, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a,
	0x6f, 0x62, 0x56, 0x65, 0x72, 0x74, 0x65, 0x78, 0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x56, 0x65, 0x72,
	0x74, 0x65, 0x78, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x65, 0x64, 0x67, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x45, 0x64, 0x67, 0x65, 0x52, 0x08, 0x6a, 0x6f,
	0x62, 0x45, 0x64, 0x67, 0x65, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x56, 0x65,
	0x72, 0x74, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x4a, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x61,
	0x78, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x22, 0x8e, 0x01,
	0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x45, 0x64, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc8,
	0x01, 0x0a, 0x13, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x4a, 0x0a, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x08, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x1a, 0x3b, 0x0a, 0x0d,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3d, 0x0a, 0x0d, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x58, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x33, 0x0a, 0x0b, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x52, 0x4f, 0x42, 0x45,
	0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49, 0x4e, 0x47,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x41, 0x47, 0x10, 0x02, 0x32, 0xec, 0x01,
	0x0a, 0x12, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a, 0x0a, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a,
	0x6f, 0x62, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x6a,
	0x6f, 0x62, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x6f, 0x0a, 0x0c, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x6a, 0x6f, 0x62, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xf2, 0x01, 0x0a,
	0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12,
	0x11, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x6d, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x64, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x6f, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x42, 0x23, 0x5a, 0x21, 0x6d, 0x70, 0x73, 0x2d, 0x6a, 0x6f, 0x62, 0x2d, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x2d, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6a, 0x6f, 0x62,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_job_v1_job_proto_rawDescOnce sync.Once
	file_job_v1_job_proto_rawDescData = file_job_v1_job_proto_rawDesc
)

func file_job_v1_job_proto_rawDescGZIP() []byte {
	file_job_v1_job_proto_rawDescOnce.Do(func() {
		file_job_v1_job_proto_rawDescData = protoimpl.X.CompressGZIP(file_job_v1_job_proto_rawDescData)
	})
	return file_job_v1_job_proto_rawDescData
}

var file_job_v1_job_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_job_v1_job_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_job_v1_job_proto_goTypes = []any{
	(ExecuteMode)(0),              // 0: api.job.v1.ExecuteMode
	(*AnalyzeJobRequest)(nil),     // 1: api.job.v1.AnalyzeJobRequest
	(*AnalyzeJobResponse)(nil),    // 2: api.job.v1.AnalyzeJobResponse
	(*ReportResultRequest)(nil),   // 3: api.job.v1.ReportResultRequest
	(*ReportResultResponse)(nil),  // 4: api.job.v1.ReportResultResponse
	(*HealthCheckRequest)(nil),    // 5: api.job.v1.HealthCheckRequest
	(*HealthCheckResponse)(nil),   // 6: api.job.v1.HealthCheckResponse
	(*ScheduleParams)(nil),        // 7: api.job.v1.ScheduleParams
	(*Input)(nil),                 // 8: api.job.v1.Input
	(*Config)(nil),                // 9: api.job.v1.Config
	(*DagJobGraph)(nil),           // 10: api.job.v1.DagJobGraph
	(*JobVertex)(nil),             // 11: api.job.v1.JobVertex
	(*JobEdge)(nil),               // 12: api.job.v1.JobEdge
	(*TaskResourceRequest)(nil),   // 13: api.job.v1.TaskResourceRequest
	(*SpeedXMessage)(nil),         // 14: api.job.v1.SpeedXMessage
	nil,                           // 15: api.job.v1.AnalyzeJobResponse.QuotaSetEntry
	nil,                           // 16: api.job.v1.AnalyzeJobResponse.OriginQuotaSetEntry
	nil,                           // 17: api.job.v1.AnalyzeJobResponse.MigrateDiscardQuotaThresholdEntry
	nil,                           // 18: api.job.v1.AnalyzeJobResponse.GraphMapEntry
	nil,                           // 19: api.job.v1.AnalyzeJobResponse.SpeedXMessageEntry
	nil,                           // 20: api.job.v1.ReportResultRequest.AllocQuotaSetEntry
	nil,                           // 21: api.job.v1.ReportResultRequest.MaxQuotaSetEntry
	nil,                           // 22: api.job.v1.ReportResultRequest.AvgQuotaSetEntry
	nil,                           // 23: api.job.v1.ScheduleParams.QuotaSetEntry
	nil,                           // 24: api.job.v1.Config.ExtendEntry
	nil,                           // 25: api.job.v1.TaskResourceRequest.QuotaSetEntry
	(*timestamppb.Timestamp)(nil), // 26: google.protobuf.Timestamp
}
var file_job_v1_job_proto_depIdxs = []int32{
	7,  // 0: api.job.v1.AnalyzeJobRequest.schedule_params:type_name -> api.job.v1.ScheduleParams
	26, // 1: api.job.v1.AnalyzeJobRequest.create_time:type_name -> google.protobuf.Timestamp
	0,  // 2: api.job.v1.AnalyzeJobResponse.execute_mode:type_name -> api.job.v1.ExecuteMode
	15, // 3: api.job.v1.AnalyzeJobResponse.quota_set:type_name -> api.job.v1.AnalyzeJobResponse.QuotaSetEntry
	16, // 4: api.job.v1.AnalyzeJobResponse.origin_quota_set:type_name -> api.job.v1.AnalyzeJobResponse.OriginQuotaSetEntry
	17, // 5: api.job.v1.AnalyzeJobResponse.migrate_discard_quota_threshold:type_name -> api.job.v1.AnalyzeJobResponse.MigrateDiscardQuotaThresholdEntry
	10, // 6: api.job.v1.AnalyzeJobResponse.graph:type_name -> api.job.v1.DagJobGraph
	18, // 7: api.job.v1.AnalyzeJobResponse.graph_map:type_name -> api.job.v1.AnalyzeJobResponse.GraphMapEntry
	19, // 8: api.job.v1.AnalyzeJobResponse.speed_x_message:type_name -> api.job.v1.AnalyzeJobResponse.SpeedXMessageEntry
	20, // 9: api.job.v1.ReportResultRequest.alloc_quota_set:type_name -> api.job.v1.ReportResultRequest.AllocQuotaSetEntry
	21, // 10: api.job.v1.ReportResultRequest.max_quota_set:type_name -> api.job.v1.ReportResultRequest.MaxQuotaSetEntry
	22, // 11: api.job.v1.ReportResultRequest.avg_quota_set:type_name -> api.job.v1.ReportResultRequest.AvgQuotaSetEntry
	26, // 12: api.job.v1.ReportResultRequest.create_time:type_name -> google.protobuf.Timestamp
	26, // 13: api.job.v1.ReportResultRequest.submit_time:type_name -> google.protobuf.Timestamp
	23, // 14: api.job.v1.ScheduleParams.quota_set:type_name -> api.job.v1.ScheduleParams.QuotaSetEntry
	8,  // 15: api.job.v1.ScheduleParams.inputs:type_name -> api.job.v1.Input
	9,  // 16: api.job.v1.ScheduleParams.configs:type_name -> api.job.v1.Config
	24, // 17: api.job.v1.Config.extend:type_name -> api.job.v1.Config.ExtendEntry
	11, // 18: api.job.v1.DagJobGraph.job_vertexs:type_name -> api.job.v1.JobVertex
	12, // 19: api.job.v1.DagJobGraph.job_edges:type_name -> api.job.v1.JobEdge
	13, // 20: api.job.v1.JobVertex.resource_request:type_name -> api.job.v1.TaskResourceRequest
	7,  // 21: api.job.v1.JobVertex.schedule_params:type_name -> api.job.v1.ScheduleParams
	25, // 22: api.job.v1.TaskResourceRequest.quota_set:type_name -> api.job.v1.TaskResourceRequest.QuotaSetEntry
	10, // 23: api.job.v1.AnalyzeJobResponse.GraphMapEntry.value:type_name -> api.job.v1.DagJobGraph
	14, // 24: api.job.v1.AnalyzeJobResponse.SpeedXMessageEntry.value:type_name -> api.job.v1.SpeedXMessage
	1,  // 25: api.job.v1.JobAnalysisService.AnalyzeJob:input_type -> api.job.v1.AnalyzeJobRequest
	3,  // 26: api.job.v1.JobAnalysisService.ReportResult:input_type -> api.job.v1.ReportResultRequest
	5,  // 27: api.job.v1.ServiceHealthService.CheckLiveness:input_type -> api.job.v1.HealthCheckRequest
	5,  // 28: api.job.v1.ServiceHealthService.CheckReadiness:input_type -> api.job.v1.HealthCheckRequest
	2,  // 29: api.job.v1.JobAnalysisService.AnalyzeJob:output_type -> api.job.v1.AnalyzeJobResponse
	4,  // 30: api.job.v1.JobAnalysisService.ReportResult:output_type -> api.job.v1.ReportResultResponse
	6,  // 31: api.job.v1.ServiceHealthService.CheckLiveness:output_type -> api.job.v1.HealthCheckResponse
	6,  // 32: api.job.v1.ServiceHealthService.CheckReadiness:output_type -> api.job.v1.HealthCheckResponse
	29, // [29:33] is the sub-list for method output_type
	25, // [25:29] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_job_v1_job_proto_init() }
func file_job_v1_job_proto_init() {
	if File_job_v1_job_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_job_v1_job_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*AnalyzeJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*AnalyzeJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ReportResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ReportResultResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*HealthCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*HealthCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ScheduleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*DagJobGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*JobVertex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*JobEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*TaskResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_v1_job_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SpeedXMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_job_v1_job_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_job_v1_job_proto_goTypes,
		DependencyIndexes: file_job_v1_job_proto_depIdxs,
		EnumInfos:         file_job_v1_job_proto_enumTypes,
		MessageInfos:      file_job_v1_job_proto_msgTypes,
	}.Build()
	File_job_v1_job_proto = out.File
	file_job_v1_job_proto_rawDesc = nil
	file_job_v1_job_proto_goTypes = nil
	file_job_v1_job_proto_depIdxs = nil
}
