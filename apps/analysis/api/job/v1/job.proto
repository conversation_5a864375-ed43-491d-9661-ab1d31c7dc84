syntax = "proto3";

package api.job.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "mps-job-analysis-go/api/job/v1;v1";

// Job Analysis Service
service JobAnalysisService {
  // 作业分析接口
  rpc AnalyzeJob(AnalyzeJobRequest) returns (AnalyzeJobResponse) {
    option (google.api.http) = {
      post: "/job/analysis"
      body: "*"
    };
  }
  
  // 结果报告接口
  rpc ReportResult(ReportResultRequest) returns (ReportResultResponse) {
    option (google.api.http) = {
      post: "/job/reportResult"
      body: "*"
    };
  }
}

// Service Health Service
service ServiceHealthService {
  // 存活性检查
  rpc CheckLiveness(HealthCheckRequest) returns (HealthCheckResponse) {
    option (google.api.http) = {
      get: "/service/liveness"
    };
  }
  
  // 就绪性检查
  rpc CheckReadiness(HealthCheckRequest) returns (HealthCheckResponse) {
    option (google.api.http) = {
      get: "/service/readiness"
    };
  }
}

// 作业分析请求
message AnalyzeJobRequest {
  string engine_model = 1 [(validate.rules).string.min_len = 1];
  string engine_params = 2 [(validate.rules).string.min_len = 1];
  string user_id = 3 [(validate.rules).string.min_len = 1];
  string job_id = 4 [(validate.rules).string.min_len = 1];
  bool slice_process = 5;
  string analysis_mode = 6;
  ScheduleParams schedule_params = 7;
  string tag = 8;
  google.protobuf.Timestamp create_time = 9;
  int32 max_slice_num = 10;
  int32 min_slice_duration = 11;
  bool use_worker_brain_result = 12;
  bool invoke_worker_brain = 13;
  repeated string speed_x_range = 14;
  bool is_auto_speed_x = 15;
  string trace = 16; // JSON string
  string pipeline_id = 17;
  string request_id = 18;
}

// 作业分析响应
message AnalyzeJobResponse {
  bool success = 1;
  string code = 2;
  string message = 3;
  ExecuteMode execute_mode = 4;
  int64 expect_cost_time = 5;
  map<string, int64> quota_set = 6;
  map<string, int64> origin_quota_set = 7;
  int64 sla_finish_delay = 8;
  int64 sla_queuing_delay = 9;
  int32 max_migrate_retry = 10;
  map<string, int64> migrate_discard_quota_threshold = 11;
  DagJobGraph graph = 12;
  map<string, DagJobGraph> graph_map = 13;
  map<string, SpeedXMessage> speed_x_message = 14;
}

// 结果报告请求
message ReportResultRequest {
  string job_id = 1 [(validate.rules).string.min_len = 1];
  map<string, int64> alloc_quota_set = 2;
  map<string, int64> max_quota_set = 3;
  map<string, int64> avg_quota_set = 4;
  string engine_model = 5;
  string engine_params = 6;
  int64 expect_cost_time = 7;
  int64 real_cost_time = 8;
  google.protobuf.Timestamp create_time = 9;
  google.protobuf.Timestamp submit_time = 10;
  string trace = 11; // JSON string
  string pipeline_id = 12;
  string tag = 13;
  string request_id = 14;
  string product = 15;
  string result_data = 16; // JSON string
  string user_id = 17;
  string env = 18;
  string station = 19;
}

// 结果报告响应
message ReportResultResponse {
  string code = 1;
  string message = 2;
}

// 健康检查请求
message HealthCheckRequest {}

// 健康检查响应
message HealthCheckResponse {
  string code = 1;
  string message = 2;
}

// 调度参数
message ScheduleParams {
  string pipeline_id = 1;
  map<string, int64> quota_set = 2;
  int32 priority = 3;
  int32 expect_cost_time = 4;
  repeated Input inputs = 5;
  repeated Config configs = 6;
  int32 parallel_num = 7;
  string sla_level = 8;
  int32 slice_num = 9;
  repeated string speed_x_range = 10;
}

// 输入参数
message Input {
  double duration = 1;
  double video_bitrate = 2;
  double avg_fps = 3;
  int64 size = 4;
  string format = 5;
  double fps = 6;
  int32 width = 7;
  string audio_codec = 8;
  double audio_bitrate = 9;
  int32 height = 10;
  string video_codec = 11;
}

// 配置参数
message Config {
  double duration = 1;
  string nh_version = 2;
  string format = 3;
  double fps = 4;
  string id = 5;
  string audio_codec = 6;
  string job_type = 7;
  string template_id = 8;
  double audio_bitrate = 9;
  string video_codec = 10;
  bool audio_only = 11;
  map<string, string> extend = 12;
  string gpu_restore = 13;
  bool by_worker_brain = 14;
}

// 执行模式枚举
enum ExecuteMode {
  PROBE_FIRST = 0;
  SINGLE = 1;
  DAG = 2;
}

// DAG作业图
message DagJobGraph {
  repeated JobVertex job_vertexs = 1;
  repeated JobEdge job_edges = 2;
}

// 作业顶点
message JobVertex {
  string name = 1;
  string engine_model = 2;
  string engine_params = 3;
  TaskResourceRequest resource_request = 4;
  ScheduleParams schedule_params = 5;
  int32 max_migrate_retry = 6;
}

// 作业边
message JobEdge {
  string name = 1;
  string from = 2;
  string to = 3;
  string mode = 4;
  string status = 5;
  string from_status = 6;
}

// 任务资源请求
message TaskResourceRequest {
  int64 expect_cost_time = 1;
  map<string, int64> quota_set = 2;
}

// 倍速消息
message SpeedXMessage {
  int32 code = 1;
  string message = 2;
}
