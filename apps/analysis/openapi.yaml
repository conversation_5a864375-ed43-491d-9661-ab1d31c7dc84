# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /job/analysis:
        post:
            tags:
                - JobAnalysisService
            description: 作业分析接口
            operationId: JobAnalysisService_AnalyzeJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.job.v1.AnalyzeJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.job.v1.AnalyzeJobResponse'
    /job/reportResult:
        post:
            tags:
                - JobAnalysisService
            description: 结果报告接口
            operationId: JobAnalysisService_ReportResult
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.job.v1.ReportResultRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.job.v1.ReportResultResponse'
    /service/liveness:
        get:
            tags:
                - ServiceHealthService
            description: 存活性检查
            operationId: ServiceHealthService_CheckLiveness
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.job.v1.HealthCheckResponse'
    /service/readiness:
        get:
            tags:
                - ServiceHealthService
            description: 就绪性检查
            operationId: ServiceHealthService_CheckReadiness
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.job.v1.HealthCheckResponse'
components:
    schemas:
        api.job.v1.AnalyzeJobRequest:
            type: object
            properties:
                engineModel:
                    type: string
                engineParams:
                    type: string
                userId:
                    type: string
                jobId:
                    type: string
                sliceProcess:
                    type: boolean
                analysisMode:
                    type: string
                scheduleParams:
                    $ref: '#/components/schemas/api.job.v1.ScheduleParams'
                tag:
                    type: string
                createTime:
                    type: string
                    format: date-time
                maxSliceNum:
                    type: integer
                    format: int32
                minSliceDuration:
                    type: integer
                    format: int32
                useWorkerBrainResult:
                    type: boolean
                invokeWorkerBrain:
                    type: boolean
                speedXRange:
                    type: array
                    items:
                        type: string
                isAutoSpeedX:
                    type: boolean
                trace:
                    type: string
                pipelineId:
                    type: string
                requestId:
                    type: string
            description: 作业分析请求
        api.job.v1.AnalyzeJobResponse:
            type: object
            properties:
                success:
                    type: boolean
                code:
                    type: string
                message:
                    type: string
                executeMode:
                    type: integer
                    format: enum
                expectCostTime:
                    type: string
                quotaSet:
                    type: object
                    additionalProperties:
                        type: string
                originQuotaSet:
                    type: object
                    additionalProperties:
                        type: string
                slaFinishDelay:
                    type: string
                slaQueuingDelay:
                    type: string
                maxMigrateRetry:
                    type: integer
                    format: int32
                migrateDiscardQuotaThreshold:
                    type: object
                    additionalProperties:
                        type: string
                graph:
                    $ref: '#/components/schemas/api.job.v1.DagJobGraph'
                graphMap:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.job.v1.DagJobGraph'
                speedXMessage:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.job.v1.SpeedXMessage'
            description: 作业分析响应
        api.job.v1.Config:
            type: object
            properties:
                duration:
                    type: number
                    format: double
                nhVersion:
                    type: string
                format:
                    type: string
                fps:
                    type: number
                    format: double
                id:
                    type: string
                audioCodec:
                    type: string
                jobType:
                    type: string
                templateId:
                    type: string
                audioBitrate:
                    type: number
                    format: double
                videoCodec:
                    type: string
                audioOnly:
                    type: boolean
                extend:
                    type: object
                    additionalProperties:
                        type: string
                gpuRestore:
                    type: string
                byWorkerBrain:
                    type: boolean
            description: 配置参数
        api.job.v1.DagJobGraph:
            type: object
            properties:
                jobVertexs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.job.v1.JobVertex'
                jobEdges:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.job.v1.JobEdge'
            description: DAG作业图
        api.job.v1.HealthCheckResponse:
            type: object
            properties:
                code:
                    type: string
                message:
                    type: string
            description: 健康检查响应
        api.job.v1.Input:
            type: object
            properties:
                duration:
                    type: number
                    format: double
                videoBitrate:
                    type: number
                    format: double
                avgFps:
                    type: number
                    format: double
                size:
                    type: string
                format:
                    type: string
                fps:
                    type: number
                    format: double
                width:
                    type: integer
                    format: int32
                audioCodec:
                    type: string
                audioBitrate:
                    type: number
                    format: double
                height:
                    type: integer
                    format: int32
                videoCodec:
                    type: string
            description: 输入参数
        api.job.v1.JobEdge:
            type: object
            properties:
                name:
                    type: string
                from:
                    type: string
                to:
                    type: string
                mode:
                    type: string
                status:
                    type: string
                fromStatus:
                    type: string
            description: 作业边
        api.job.v1.JobVertex:
            type: object
            properties:
                name:
                    type: string
                engineModel:
                    type: string
                engineParams:
                    type: string
                resourceRequest:
                    $ref: '#/components/schemas/api.job.v1.TaskResourceRequest'
                scheduleParams:
                    $ref: '#/components/schemas/api.job.v1.ScheduleParams'
                maxMigrateRetry:
                    type: integer
                    format: int32
            description: 作业顶点
        api.job.v1.ReportResultRequest:
            type: object
            properties:
                jobId:
                    type: string
                allocQuotaSet:
                    type: object
                    additionalProperties:
                        type: string
                maxQuotaSet:
                    type: object
                    additionalProperties:
                        type: string
                avgQuotaSet:
                    type: object
                    additionalProperties:
                        type: string
                engineModel:
                    type: string
                engineParams:
                    type: string
                expectCostTime:
                    type: string
                realCostTime:
                    type: string
                createTime:
                    type: string
                    format: date-time
                submitTime:
                    type: string
                    format: date-time
                trace:
                    type: string
                pipelineId:
                    type: string
                tag:
                    type: string
                requestId:
                    type: string
                product:
                    type: string
                resultData:
                    type: string
                userId:
                    type: string
                env:
                    type: string
                station:
                    type: string
            description: 结果报告请求
        api.job.v1.ReportResultResponse:
            type: object
            properties:
                code:
                    type: string
                message:
                    type: string
            description: 结果报告响应
        api.job.v1.ScheduleParams:
            type: object
            properties:
                pipelineId:
                    type: string
                quotaSet:
                    type: object
                    additionalProperties:
                        type: string
                priority:
                    type: integer
                    format: int32
                expectCostTime:
                    type: integer
                    format: int32
                inputs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.job.v1.Input'
                configs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.job.v1.Config'
                parallelNum:
                    type: integer
                    format: int32
                slaLevel:
                    type: string
                sliceNum:
                    type: integer
                    format: int32
                speedXRange:
                    type: array
                    items:
                        type: string
            description: 调度参数
        api.job.v1.SpeedXMessage:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                message:
                    type: string
            description: 倍速消息
        api.job.v1.TaskResourceRequest:
            type: object
            properties:
                expectCostTime:
                    type: string
                quotaSet:
                    type: object
                    additionalProperties:
                        type: string
            description: 任务资源请求
tags:
    - name: JobAnalysisService
      description: Job Analysis Service
    - name: ServiceHealthService
      description: Service Health Service
