#!/bin/bash

# MPS Job Analysis API 基于curl的综合测试脚本
# 基于 curl分析接口测试.txt 和 data.sql 生成的测试用例

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$PROJECT_DIR/test-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_PREFIX="curl_based_test_$TIMESTAMP"

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go 版本: $GO_VERSION"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 未运行"
        exit 1
    fi
    
    log_info "Docker 版本: $(docker --version)"
    
    # 检查测试文件
    if [ ! -f "$PROJECT_DIR/test/curl_based_test.go" ]; then
        log_error "测试文件不存在: $PROJECT_DIR/test/curl_based_test.go"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/scripts/data.sql" ]; then
        log_error "数据文件不存在: $PROJECT_DIR/scripts/data.sql"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 准备测试环境
prepare_environment() {
    log_info "准备测试环境..."
    
    # 创建报告目录
    mkdir -p "$REPORTS_DIR"
    
    # 进入项目目录
    cd "$PROJECT_DIR"
    
    # 下载依赖
    log_info "下载Go模块依赖..."
    go mod download
    
    # 清理可能存在的容器
    log_info "清理旧的测试容器..."
    docker container prune -f || true
    
    log_success "测试环境准备完成"
}

# 运行测试
run_tests() {
    log_info "开始运行基于curl的综合测试..."
    
    cd "$PROJECT_DIR"
    
    # 设置测试环境变量
    export CGO_ENABLED=1
    export GOOS=linux
    
    # 运行测试并生成详细输出
    log_info "执行测试套件..."
    
    # 创建测试输出文件
    TEST_OUTPUT="$REPORTS_DIR/${REPORT_PREFIX}_output.log"
    TEST_JSON="$REPORTS_DIR/${REPORT_PREFIX}_results.json"
    
    # 运行测试，同时输出到控制台和文件
    if go test -v -race -timeout=30m \
        -coverprofile="$REPORTS_DIR/${REPORT_PREFIX}_coverage.out" \
        -json \
        ./test -run TestCurlBasedSuite 2>&1 | tee "$TEST_OUTPUT"; then
        
        log_success "测试执行完成"
        
        # 生成覆盖率报告
        if [ -f "$REPORTS_DIR/${REPORT_PREFIX}_coverage.out" ]; then
            log_info "生成覆盖率报告..."
            go tool cover -html="$REPORTS_DIR/${REPORT_PREFIX}_coverage.out" \
                -o "$REPORTS_DIR/${REPORT_PREFIX}_coverage.html"
            
            # 显示覆盖率统计
            COVERAGE=$(go tool cover -func="$REPORTS_DIR/${REPORT_PREFIX}_coverage.out" | grep total | awk '{print $3}')
            log_info "代码覆盖率: $COVERAGE"
        fi
        
        return 0
    else
        log_error "测试执行失败"
        return 1
    fi
}

# 生成测试报告
generate_reports() {
    log_info "生成测试报告..."
    
    # 解析测试输出
    if [ -f "$TEST_OUTPUT" ]; then
        # 统计测试结果
        TOTAL_TESTS=$(grep -c "=== RUN" "$TEST_OUTPUT" || echo "0")
        PASSED_TESTS=$(grep -c "--- PASS:" "$TEST_OUTPUT" || echo "0")
        FAILED_TESTS=$(grep -c "--- FAIL:" "$TEST_OUTPUT" || echo "0")
        SKIPPED_TESTS=$(grep -c "--- SKIP:" "$TEST_OUTPUT" || echo "0")
        
        # 计算成功率
        if [ "$TOTAL_TESTS" -gt 0 ]; then
            SUCCESS_RATE=$(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
        else
            SUCCESS_RATE="0"
        fi
        
        # 生成摘要报告
        SUMMARY_REPORT="$REPORTS_DIR/${REPORT_PREFIX}_summary.txt"
        cat > "$SUMMARY_REPORT" << EOF
MPS Job Analysis API 基于curl的综合测试报告
============================================

测试时间: $(date)
报告ID: $REPORT_PREFIX

测试统计:
- 总测试数: $TOTAL_TESTS
- 通过: $PASSED_TESTS
- 失败: $FAILED_TESTS  
- 跳过: $SKIPPED_TESTS
- 成功率: ${SUCCESS_RATE}%

测试覆盖的功能:
1. DAG分析失败案例 (基于curl测试文件案例1)
2. DAG分析成功案例 (基于curl测试文件案例2和3)
3. 引擎配额配置测试 (基于data.sql配置)
4. 错误处理和边界条件测试
5. 性能和并发测试

数据源:
- curl测试文件: docs/curl分析接口测试.txt
- 数据库配置: scripts/data.sql (137条配置记录)
- 测试框架: testify + testcontainers-go
- 数据库: MySQL 8.0 (Docker容器)

文件位置:
- 详细日志: $TEST_OUTPUT
- 覆盖率报告: $REPORTS_DIR/${REPORT_PREFIX}_coverage.html
- 摘要报告: $SUMMARY_REPORT

EOF

        # 如果有失败的测试，添加失败详情
        if [ "$FAILED_TESTS" -gt 0 ]; then
            echo "" >> "$SUMMARY_REPORT"
            echo "失败的测试详情:" >> "$SUMMARY_REPORT"
            echo "==================" >> "$SUMMARY_REPORT"
            grep -A 5 "--- FAIL:" "$TEST_OUTPUT" >> "$SUMMARY_REPORT" || true
        fi
        
        log_success "测试报告生成完成"
        
        # 显示摘要
        echo ""
        log_info "测试结果摘要:"
        echo "  总测试数: $TOTAL_TESTS"
        echo "  通过: $PASSED_TESTS"
        echo "  失败: $FAILED_TESTS"
        echo "  跳过: $SKIPPED_TESTS"
        echo "  成功率: ${SUCCESS_RATE}%"
        echo ""
        
        if [ "$FAILED_TESTS" -gt 0 ]; then
            log_warning "有 $FAILED_TESTS 个测试失败，请查看详细报告"
            return 1
        else
            log_success "所有测试通过！"
            return 0
        fi
    else
        log_error "未找到测试输出文件"
        return 1
    fi
}

# 清理资源
cleanup() {
    log_info "清理测试资源..."
    
    # 清理Docker容器
    docker container prune -f || true
    
    # 清理临时文件
    rm -f "$PROJECT_DIR"/*.tmp || true
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
MPS Job Analysis API 基于curl的综合测试脚本

用法: $0 [选项]

选项:
  -h, --help     显示此帮助信息
  -v, --verbose  详细输出模式
  -c, --clean    仅清理资源，不运行测试
  --no-cleanup   测试后不清理资源

示例:
  $0                    # 运行完整测试
  $0 --verbose          # 详细模式运行测试
  $0 --clean            # 仅清理资源
  $0 --no-cleanup       # 测试后保留资源

测试内容:
  - 基于curl分析接口测试.txt的真实场景测试
  - 基于data.sql的引擎配额配置测试
  - API错误处理和边界条件测试
  - 性能和并发测试
  - 自动生成详细的测试报告

报告输出目录: $REPORTS_DIR
EOF
}

# 主函数
main() {
    local verbose=false
    local clean_only=false
    local no_cleanup=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -c|--clean)
                clean_only=true
                shift
                ;;
            --no-cleanup)
                no_cleanup=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细模式
    if [ "$verbose" = true ]; then
        set -x
    fi
    
    # 如果只是清理
    if [ "$clean_only" = true ]; then
        cleanup
        exit 0
    fi
    
    # 主要流程
    local exit_code=0
    
    echo "========================================"
    echo "MPS Job Analysis API 基于curl的综合测试"
    echo "========================================"
    echo ""
    
    # 执行测试流程
    check_dependencies || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        prepare_environment || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        run_tests || exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        generate_reports || exit_code=$?
    fi
    
    # 清理资源（除非指定不清理）
    if [ "$no_cleanup" != true ]; then
        cleanup
    fi
    
    echo ""
    echo "========================================"
    if [ $exit_code -eq 0 ]; then
        log_success "测试完成！所有测试通过。"
        echo "报告位置: $REPORTS_DIR"
    else
        log_error "测试失败！请查看详细报告。"
        echo "报告位置: $REPORTS_DIR"
    fi
    echo "========================================"
    
    exit $exit_code
}

# 捕获中断信号
trap cleanup EXIT

# 运行主函数
main "$@"
