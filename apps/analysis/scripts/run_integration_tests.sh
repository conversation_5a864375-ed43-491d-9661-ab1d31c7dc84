#!/bin/bash

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "=== MPS Job Analysis Integration Tests ==="
echo "Project directory: $PROJECT_DIR"

# 检查 Docker 是否可用
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH"
    echo "Please install Docker to run integration tests"
    exit 1
fi

# 检查 Docker 是否运行
if ! docker info &> /dev/null; then
    echo "Error: Docker is not running"
    echo "Please start Docker daemon to run integration tests"
    exit 1
fi

echo "✓ Docker is available and running"

# 进入项目目录
cd "$PROJECT_DIR"

# 设置环境变量
export CGO_ENABLED=1
export GO111MODULE=on

# 清理之前的测试容器（如果存在）
echo "Cleaning up any existing test containers..."
docker container prune -f --filter "label=org.testcontainers=true" || true

echo ""
echo "=== Running Data Layer Integration Tests ==="
go test -v -tags=integration ./internal/data -run TestIntegrationSuite

echo ""
echo "=== Running Business Logic Integration Tests ==="
go test -v -tags=integration ./internal/biz -run TestBizIntegrationSuite

echo ""
echo "=== Running End-to-End Tests ==="
go test -v -tags=integration ./test -run TestE2ESuite

echo ""
echo "=== Running All Integration Tests with Coverage ==="
go test -v -tags=integration -coverprofile=coverage.out -covermode=atomic \
    ./internal/data \
    ./internal/biz \
    ./test

# 生成覆盖率报告
if command -v go &> /dev/null; then
    echo ""
    echo "=== Coverage Report ==="
    go tool cover -func=coverage.out | tail -1
    
    # 生成 HTML 覆盖率报告
    if [[ "$1" == "--html" ]]; then
        echo "Generating HTML coverage report..."
        go tool cover -html=coverage.out -o coverage.html
        echo "Coverage report saved to coverage.html"
    fi
fi

# 清理测试容器
echo ""
echo "Cleaning up test containers..."
docker container prune -f --filter "label=org.testcontainers=true" || true

echo ""
echo "✓ All integration tests completed successfully!"
