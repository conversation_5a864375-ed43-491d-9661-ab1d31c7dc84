#!/bin/bash

# MPS Job Analysis API 测试问题修复脚本
# 基于测试报告中发现的问题进行修复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示修复计划
show_fix_plan() {
    cat << EOF
=== MPS Job Analysis API 测试问题修复计划 ===

基于测试报告发现的问题，将进行以下修复：

🔴 P0 - 立即修复:
1. 数据库连接管理问题
   - 修复测试套件中的数据库连接生命周期
   - 确保连接在测试期间保持活跃

2. API响应格式问题  
   - 统一code字段为字符串类型
   - 修复JSON序列化问题

🟡 P1 - 功能完善:
3. 业务逻辑问题
   - 完善mergeWorkerBrainResult方法
   - 修复generateDAGGraph空指针问题
   - 确保配额设置正确传递

🟢 P2 - 测试改进:
4. 测试稳定性
   - 改进资源管理
   - 增加错误处理

是否继续执行修复? (y/N)
EOF
}

# 修复1: 数据库连接管理
fix_database_connection() {
    log_info "修复数据库连接管理问题..."
    
    # 备份原文件
    cp "$PROJECT_DIR/test/curl_based_test.go" "$PROJECT_DIR/test/curl_based_test.go.backup"
    
    # 修复数据库连接生命周期问题
    # 这里应该修改测试套件，确保数据库连接在整个测试期间保持活跃
    log_warning "数据库连接修复需要手动调整测试套件结构"
    log_info "建议: 将数据库连接移到SetupSuite中，并在TearDownSuite中关闭"
    
    cat > "$PROJECT_DIR/test/db_connection_fix.patch" << 'EOF'
--- a/test/curl_based_test.go
+++ b/test/curl_based_test.go
@@ -45,6 +45,7 @@ type CurlBasedTestSuite struct {
 	httpServer     *kratoshttp.Server
 	baseURL        string
 	ctx            context.Context
+	db             *gorm.DB
 }
 
 // SetupSuite 在所有测试开始前运行
@@ -72,6 +73,8 @@ func (suite *CurlBasedTestSuite) SetupSuite() {
 	db, err := gorm.Open(mysqldriver.Open(dsn), &gorm.Config{})
 	require.NoError(suite.T(), err)
 
+	suite.db = db
+
 	// 创建数据访问层 - 使用NewData函数创建
 	dataLayer, cleanup, err := data.NewData(nil, log.DefaultLogger, db, nil)
 	require.NoError(suite.T(), err)
EOF
    
    log_success "数据库连接修复补丁已生成: $PROJECT_DIR/test/db_connection_fix.patch"
}

# 修复2: API响应格式
fix_api_response_format() {
    log_info "修复API响应格式问题..."
    
    # 检查API定义文件
    API_FILE="$PROJECT_DIR/../../../api/job/v1/job_analysis.proto"
    if [ -f "$API_FILE" ]; then
        log_info "检查API定义文件: $API_FILE"
        
        # 检查code字段定义
        if grep -q "string code" "$API_FILE"; then
            log_success "API定义中code字段已定义为string类型"
        else
            log_warning "需要检查API定义中的code字段类型"
        fi
    else
        log_warning "未找到API定义文件"
    fi
    
    # 检查服务层实现
    SERVICE_FILE="$PROJECT_DIR/internal/service/job_analysis.go"
    if [ -f "$SERVICE_FILE" ]; then
        log_info "检查服务层实现..."
        
        # 查找可能的类型转换问题
        if grep -n "Code.*=" "$SERVICE_FILE"; then
            log_info "找到Code字段赋值，请检查类型是否正确"
        fi
    fi
    
    log_success "API响应格式检查完成"
}

# 修复3: 业务逻辑问题
fix_business_logic() {
    log_info "修复业务逻辑问题..."
    
    # 备份业务逻辑文件
    BIZ_FILE="$PROJECT_DIR/internal/biz/job_analysis.go"
    cp "$BIZ_FILE" "$BIZ_FILE.backup"
    
    # 检查mergeWorkerBrainResult方法
    if grep -A 20 "mergeWorkerBrainResult" "$BIZ_FILE" | grep -q "TODO\|FIXME\|未实现"; then
        log_warning "发现mergeWorkerBrainResult方法需要完善"
    fi
    
    # 检查generateDAGGraph方法
    if grep -A 20 "generateDAGGraph" "$BIZ_FILE" | grep -q "ExpectCostTime.*\*"; then
        log_info "generateDAGGraph方法中的空指针问题已在之前修复"
    fi
    
    # 生成业务逻辑修复建议
    cat > "$PROJECT_DIR/test/business_logic_fixes.md" << 'EOF'
# 业务逻辑修复建议

## 1. mergeWorkerBrainResult方法
当前实现不完整，建议：
- 正确解析WorkerBrain响应
- 合并配额设置到结果中
- 处理期望耗时字段

## 2. 配额设置传递
确保从数据库查询的配额正确传递到最终响应：
- 检查JobAnalysisResult到AnalyzeJobResponse的转换
- 验证配额字段映射

## 3. 错误处理
增强错误处理逻辑：
- 数据库查询失败时的降级处理
- 外部服务调用失败时的处理
- 参数验证错误的详细信息
EOF
    
    log_success "业务逻辑修复建议已生成: $PROJECT_DIR/test/business_logic_fixes.md"
}

# 修复4: 测试稳定性
fix_test_stability() {
    log_info "改进测试稳定性..."
    
    # 生成改进的测试配置
    cat > "$PROJECT_DIR/test/improved_test_config.go" << 'EOF'
package test

import (
    "context"
    "time"
)

// TestConfig 测试配置
type TestConfig struct {
    DatabaseTimeout    time.Duration
    ServerStartTimeout time.Duration
    RequestTimeout     time.Duration
    RetryAttempts      int
    RetryDelay         time.Duration
}

// DefaultTestConfig 默认测试配置
func DefaultTestConfig() *TestConfig {
    return &TestConfig{
        DatabaseTimeout:    30 * time.Second,
        ServerStartTimeout: 10 * time.Second,
        RequestTimeout:     5 * time.Second,
        RetryAttempts:      3,
        RetryDelay:         1 * time.Second,
    }
}

// WithRetry 重试执行函数
func WithRetry(ctx context.Context, attempts int, delay time.Duration, fn func() error) error {
    var lastErr error
    for i := 0; i < attempts; i++ {
        if err := fn(); err != nil {
            lastErr = err
            if i < attempts-1 {
                select {
                case <-ctx.Done():
                    return ctx.Err()
                case <-time.After(delay):
                    continue
                }
            }
        } else {
            return nil
        }
    }
    return lastErr
}
EOF
    
    log_success "测试稳定性改进配置已生成: $PROJECT_DIR/test/improved_test_config.go"
}

# 生成修复验证脚本
generate_verification_script() {
    log_info "生成修复验证脚本..."
    
    cat > "$PROJECT_DIR/scripts/verify_fixes.sh" << 'EOF'
#!/bin/bash

# 修复验证脚本

set -e

echo "=== 验证修复效果 ==="

# 1. 验证数据库连接
echo "1. 验证数据库连接..."
cd "$(dirname "$0")/.."
go test -v ./test -run TestQuickValidation/Database_Config_Query -timeout=5m

# 2. 验证API响应格式
echo "2. 验证API响应格式..."
go test -v ./test -run TestQuickValidation/Basic_Analysis -timeout=5m

# 3. 验证业务逻辑
echo "3. 验证业务逻辑..."
go test -v ./test -run TestQuickValidation/Real_Curl_Data_Analysis -timeout=5m

echo "=== 验证完成 ==="
EOF
    
    chmod +x "$PROJECT_DIR/scripts/verify_fixes.sh"
    log_success "修复验证脚本已生成: $PROJECT_DIR/scripts/verify_fixes.sh"
}

# 生成修复报告
generate_fix_report() {
    log_info "生成修复报告..."
    
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    REPORT_FILE="$PROJECT_DIR/test-reports/fix_report_$TIMESTAMP.md"
    
    cat > "$REPORT_FILE" << EOF
# 测试问题修复报告

**修复时间**: $(date)
**修复版本**: $TIMESTAMP

## 修复内容

### 1. 数据库连接管理 🔧
- 生成了数据库连接修复补丁
- 建议将连接管理移到测试套件级别
- 文件: test/db_connection_fix.patch

### 2. API响应格式 ✅
- 检查了API定义和服务层实现
- 确认code字段类型定义
- 需要进一步验证序列化逻辑

### 3. 业务逻辑完善 📝
- 生成了业务逻辑修复建议
- 重点关注mergeWorkerBrainResult方法
- 文件: test/business_logic_fixes.md

### 4. 测试稳定性改进 🚀
- 创建了改进的测试配置
- 添加了重试机制
- 文件: test/improved_test_config.go

## 验证步骤

运行验证脚本:
\`\`\`bash
./scripts/verify_fixes.sh
\`\`\`

## 下一步

1. 应用数据库连接修复补丁
2. 实施业务逻辑改进建议
3. 运行完整测试套件验证
4. 更新文档和测试用例

---
**生成工具**: fix_test_issues.sh
**基于**: 综合测试报告发现的问题
EOF
    
    log_success "修复报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    echo "========================================"
    echo "MPS Job Analysis API 测试问题修复工具"
    echo "========================================"
    echo ""
    
    show_fix_plan
    
    read -p "是否继续执行修复? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "修复已取消"
        exit 0
    fi
    
    echo ""
    log_info "开始执行修复..."
    
    # 执行修复步骤
    fix_database_connection
    fix_api_response_format  
    fix_business_logic
    fix_test_stability
    generate_verification_script
    generate_fix_report
    
    echo ""
    echo "========================================"
    log_success "修复完成！"
    echo "========================================"
    echo ""
    echo "修复内容:"
    echo "  ✓ 数据库连接管理补丁"
    echo "  ✓ API响应格式检查"
    echo "  ✓ 业务逻辑修复建议"
    echo "  ✓ 测试稳定性改进"
    echo "  ✓ 验证脚本生成"
    echo ""
    echo "下一步:"
    echo "  1. 查看生成的修复文件"
    echo "  2. 应用修复建议"
    echo "  3. 运行验证脚本: ./scripts/verify_fixes.sh"
    echo ""
}

# 运行主函数
main "$@"
