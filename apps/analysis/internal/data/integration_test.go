package data

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
	mysqldriver "gorm.io/driver/mysql"
	"gorm.io/gorm"

	"mps-job-analysis-go/internal/biz"
)

// IntegrationTestSuite 集成测试套件
type IntegrationTestSuite struct {
	suite.Suite
	mysqlContainer testcontainers.Container
	db             *gorm.DB
	repo           biz.JobAnalysisRepo
	ctx            context.Context
}

// SetupSuite 在所有测试开始前运行
func (suite *IntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 启动 MySQL 容器
	mysqlContainer, err := mysql.Run(suite.ctx,
		"mysql:8.0",
		mysql.WithDatabase("testdb"),
		mysql.WithUsername("testuser"),
		mysql.WithPassword("testpass"),
		mysql.WithScripts(filepath.Join("..", "..", "scripts", "data.sql")),
	)
	require.NoError(suite.T(), err)

	suite.mysqlContainer = mysqlContainer

	// 获取连接信息
	host, err := mysqlContainer.Host(suite.ctx)
	require.NoError(suite.T(), err)

	port, err := mysqlContainer.MappedPort(suite.ctx, "3306")
	require.NoError(suite.T(), err)

	// 创建数据库连接
	dsn := fmt.Sprintf("testuser:testpass@tcp(%s:%s)/testdb?charset=utf8mb4&parseTime=True&loc=Local",
		host, port.Port())

	suite.db, err = gorm.Open(mysqldriver.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)

	// 创建数据访问层
	dataLayer := &Data{
		db:  suite.db,
		rdb: nil, // Redis 在这个测试中不需要
		log: log.NewHelper(log.DefaultLogger),
	}

	suite.repo = NewJobAnalysisRepo(dataLayer, log.DefaultLogger)
}

// TearDownSuite 在所有测试结束后运行
func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.mysqlContainer != nil {
		err := suite.mysqlContainer.Terminate(suite.ctx)
		require.NoError(suite.T(), err)
	}
}

// TestGetEngineQuotaConfig 测试获取引擎配额配置
func (suite *IntegrationTestSuite) TestGetEngineQuotaConfig() {
	tests := []struct {
		name           string
		configName     string
		expectedConfig *biz.EngineQuotaConfig
		expectError    bool
	}{
		{
			name:       "get existing config - analysis",
			configName: "analysis",
			expectedConfig: &biz.EngineQuotaConfig{
				Name:    "analysis",
				Configs: `{"jobType":"analysis"}`,
				QuotaSet: map[string]int64{
					"cpu": 1000,
				},
				Speed:     &[]float64{50.0}[0],
				DiskRatio: &[]float64{1.0}[0],
			},
			expectError: false,
		},
		{
			name:       "get existing config - editing",
			configName: "editing",
			expectedConfig: &biz.EngineQuotaConfig{
				Name:    "editing",
				Configs: `{"jobType":"editing"}`,
				QuotaSet: map[string]int64{
					"cpu": 8000,
				},
				DiskQuota:       &[]uint64{10000}[0],
				MaxMigrateRetry: 5,
				Cost:            &[]uint64{180}[0],
			},
			expectError: false,
		},
		{
			name:           "get non-existing config",
			configName:     "non-existing",
			expectedConfig: nil,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			config, err := suite.repo.GetEngineQuotaConfig(suite.ctx, tt.configName)

			if tt.expectError {
				assert.Error(suite.T(), err)
			} else {
				assert.NoError(suite.T(), err)
			}

			if tt.expectedConfig == nil {
				assert.Nil(suite.T(), config)
			} else {
				require.NotNil(suite.T(), config)
				assert.Equal(suite.T(), tt.expectedConfig.Name, config.Name)
				assert.Equal(suite.T(), tt.expectedConfig.Configs, config.Configs)
				assert.Equal(suite.T(), tt.expectedConfig.QuotaSet, config.QuotaSet)

				if tt.expectedConfig.Speed != nil {
					assert.Equal(suite.T(), tt.expectedConfig.Speed, config.Speed)
				}
				if tt.expectedConfig.DiskRatio != nil {
					assert.Equal(suite.T(), tt.expectedConfig.DiskRatio, config.DiskRatio)
				}
				if tt.expectedConfig.DiskQuota != nil {
					assert.Equal(suite.T(), tt.expectedConfig.DiskQuota, config.DiskQuota)
				}
				if tt.expectedConfig.MaxMigrateRetry != 0 {
					assert.Equal(suite.T(), tt.expectedConfig.MaxMigrateRetry, config.MaxMigrateRetry)
				}
				if tt.expectedConfig.Cost != nil {
					assert.Equal(suite.T(), tt.expectedConfig.Cost, config.Cost)
				}
			}
		})
	}
}

// TestSaveEngineQuotaConfig 测试保存引擎配额配置
func (suite *IntegrationTestSuite) TestSaveEngineQuotaConfig() {
	// 创建测试配置
	testConfig := &biz.EngineQuotaConfig{
		Name:    "test-config",
		Configs: `{"jobType":"test","testParam":"value"}`,
		QuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 500000,
		},
		Speed:           &[]float64{1.5}[0],
		DiskRatio:       &[]float64{2.0}[0],
		DiskQuota:       &[]uint64{5000}[0],
		MaxMigrateRetry: 3,
		MigrateDiscardQuotaThreshold: map[string]int64{
			"cpu": 1500,
		},
		Cost: &[]uint64{120}[0],
	}

	// 保存配置
	err := suite.repo.SaveEngineQuotaConfig(suite.ctx, testConfig)
	assert.NoError(suite.T(), err)

	// 验证保存的配置
	savedConfig, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "test-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedConfig)

	assert.Equal(suite.T(), testConfig.Name, savedConfig.Name)
	assert.Equal(suite.T(), testConfig.Configs, savedConfig.Configs)
	assert.Equal(suite.T(), testConfig.QuotaSet, savedConfig.QuotaSet)
	assert.Equal(suite.T(), testConfig.Speed, savedConfig.Speed)
	assert.Equal(suite.T(), testConfig.DiskRatio, savedConfig.DiskRatio)
	assert.Equal(suite.T(), testConfig.DiskQuota, savedConfig.DiskQuota)
	assert.Equal(suite.T(), testConfig.MaxMigrateRetry, savedConfig.MaxMigrateRetry)
	assert.Equal(suite.T(), testConfig.MigrateDiscardQuotaThreshold, savedConfig.MigrateDiscardQuotaThreshold)
	assert.Equal(suite.T(), testConfig.Cost, savedConfig.Cost)

	// 清理测试数据
	suite.cleanupTestConfig("test-config")
}

// TestUpdateEngineQuotaConfig 测试更新引擎配额配置
func (suite *IntegrationTestSuite) TestUpdateEngineQuotaConfig() {
	// 首先创建一个配置
	originalConfig := &biz.EngineQuotaConfig{
		Name:    "update-test-config",
		Configs: `{"jobType":"update-test"}`,
		QuotaSet: map[string]int64{
			"cpu": 1000,
		},
		Speed:           &[]float64{1.0}[0],
		MaxMigrateRetry: 5,
	}

	err := suite.repo.SaveEngineQuotaConfig(suite.ctx, originalConfig)
	assert.NoError(suite.T(), err)

	// 更新配置
	updatedConfig := &biz.EngineQuotaConfig{
		Name:    "update-test-config",
		Configs: `{"jobType":"update-test","updated":"true"}`,
		QuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		Speed:           &[]float64{2.0}[0],
		DiskRatio:       &[]float64{1.5}[0],
		MaxMigrateRetry: 3,
	}

	err = suite.repo.SaveEngineQuotaConfig(suite.ctx, updatedConfig)
	assert.NoError(suite.T(), err)

	// 验证更新的配置
	savedConfig, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "update-test-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedConfig)

	assert.Equal(suite.T(), updatedConfig.Configs, savedConfig.Configs)
	assert.Equal(suite.T(), updatedConfig.QuotaSet, savedConfig.QuotaSet)
	assert.Equal(suite.T(), updatedConfig.Speed, savedConfig.Speed)
	assert.Equal(suite.T(), updatedConfig.DiskRatio, savedConfig.DiskRatio)
	assert.Equal(suite.T(), updatedConfig.MaxMigrateRetry, savedConfig.MaxMigrateRetry)

	// 清理测试数据
	suite.cleanupTestConfig("update-test-config")
}

// TestListEngineQuotaConfigs 测试列出引擎配额配置
func (suite *IntegrationTestSuite) TestListEngineQuotaConfigs() {
	// 测试分页查询
	configs, err := suite.repo.ListEngineQuotaConfigs(suite.ctx, 10, 0)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), configs)
	assert.LessOrEqual(suite.T(), len(configs), 10)

	// 验证返回的配置包含预期的数据
	foundAnalysis := false
	foundEditing := false
	for _, config := range configs {
		if config.Name == "analysis" {
			foundAnalysis = true
			assert.Equal(suite.T(), `{"jobType":"analysis"}`, config.Configs)
		}
		if config.Name == "editing" {
			foundEditing = true
			assert.Equal(suite.T(), `{"jobType":"editing"}`, config.Configs)
		}
	}
	assert.True(suite.T(), foundAnalysis, "Should find analysis config")
	assert.True(suite.T(), foundEditing, "Should find editing config")

	// 测试偏移量
	configs2, err := suite.repo.ListEngineQuotaConfigs(suite.ctx, 5, 5)
	assert.NoError(suite.T(), err)
	assert.LessOrEqual(suite.T(), len(configs2), 5)
}

// cleanupTestConfig 清理测试配置
func (suite *IntegrationTestSuite) cleanupTestConfig(name string) {
	sqlDB, err := suite.db.DB()
	if err != nil {
		return
	}

	_, err = sqlDB.Exec("DELETE FROM mps_engine_quota_config WHERE name = ?", name)
	if err != nil {
		suite.T().Logf("Failed to cleanup test config %s: %v", name, err)
	}
}

// TestIntegrationSuite 运行集成测试套件
func TestIntegrationSuite(t *testing.T) {
	// 检查是否在 CI 环境中或者有 Docker
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping integration tests")
	}

	suite.Run(t, new(IntegrationTestSuite))
}
