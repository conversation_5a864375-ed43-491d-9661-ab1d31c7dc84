package data

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"mps-job-analysis-go/internal/biz"
)

// BenchmarkGetEngineQuotaConfig 基准测试获取引擎配额配置
func BenchmarkGetEngineQuotaConfig(b *testing.B) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		b.<PERSON>al(err)
	}

	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	if err != nil {
		b.<PERSON><PERSON>(err)
	}

	// 插入测试数据
	testConfig := &MpsEngineQuotaConfig{
		Name:            "benchmark-config",
		Configs:         `{"engine":"benchmark","version":"1.0"}`,
		QuotaSet:        `{"cpu":1000,"gpu":0,"disk":1000}`,
		GmtCreate:       time.Now(),
		GmtModified:     time.Now(),
		MaxMigrateRetry: 5,
	}
	err = db.Create(testConfig).Error
	if err != nil {
		b.Fatal(err)
	}

	// 创建仓库
	dataLayer := &Data{
		db:  db,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := repo.GetEngineQuotaConfig(ctx, "benchmark-config")
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkSaveEngineQuotaConfig 基准测试保存引擎配额配置
func BenchmarkSaveEngineQuotaConfig(b *testing.B) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		b.Fatal(err)
	}

	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	if err != nil {
		b.Fatal(err)
	}

	// 创建仓库
	dataLayer := &Data{
		db:  db,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		config := &biz.EngineQuotaConfig{
			Name:    fmt.Sprintf("benchmark-config-%d", i),
			Configs: `{"engine":"benchmark","version":"1.0"}`,
			QuotaSet: map[string]int64{
				"cpu": 1000,
				"gpu": 0,
			},
			MaxMigrateRetry: 5,
		}

		err := repo.SaveEngineQuotaConfig(ctx, config)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkListEngineQuotaConfigs 基准测试列出引擎配额配置
func BenchmarkListEngineQuotaConfigs(b *testing.B) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		b.Fatal(err)
	}

	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	if err != nil {
		b.Fatal(err)
	}

	// 插入多个测试数据
	for i := 0; i < 100; i++ {
		testConfig := &MpsEngineQuotaConfig{
			Name:            fmt.Sprintf("benchmark-config-%d", i),
			Configs:         fmt.Sprintf(`{"engine":"benchmark","version":"1.0","index":%d}`, i),
			QuotaSet:        `{"cpu":1000,"gpu":0,"disk":1000}`,
			GmtCreate:       time.Now(),
			GmtModified:     time.Now(),
			MaxMigrateRetry: 5,
		}
		err = db.Create(testConfig).Error
		if err != nil {
			b.Fatal(err)
		}
	}

	// 创建仓库
	dataLayer := &Data{
		db:  db,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := repo.ListEngineQuotaConfigs(ctx, 10, 0)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkGenerateCacheKey 基准测试缓存键生成
func BenchmarkGenerateCacheKey(b *testing.B) {
	// 创建仓库
	dataLayer := &Data{
		db:  nil,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	param := &biz.JobAnalysisParam{
		EngineModel:  "mps-transcode-new",
		EngineParams: `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"},"audio":{"codec":"aac","bitrate":"128k"}}`,
		UserID:       "test-user-123",
		JobID:        "test-job-456",
		AnalysisMode: "byParseEngineParams",
		Tag:          "test",
		PipelineID:   "test-pipeline",
		RequestID:    "test-request",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = repo.GenerateCacheKey(param)
	}
}

// BenchmarkComplexQuotaSetSerialization 基准测试复杂配额集合的序列化
func BenchmarkComplexQuotaSetSerialization(b *testing.B) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		b.Fatal(err)
	}

	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	if err != nil {
		b.Fatal(err)
	}

	// 创建仓库
	dataLayer := &Data{
		db:  db,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	ctx := context.Background()

	// 创建复杂的配额配置
	complexConfig := &biz.EngineQuotaConfig{
		Name:    "complex-benchmark-config",
		Configs: `{"engine":"complex","features":["gpu","cpu","memory","disk","network"],"parameters":{"quality":"high","speed":"fast"}}`,
		QuotaSet: map[string]int64{
			"cpu":     4000,
			"gpu":     2000000,
			"memory":  8000000000,
			"disk":    100000000000,
			"network": 1000000,
		},
		Speed:     &[]float64{0.75}[0],
		DiskRatio: &[]float64{3.5}[0],
		DiskQuota: &[]uint64{50000}[0],
		MigrateDiscardQuotaThreshold: map[string]int64{
			"cpu":    3000,
			"gpu":    1500000,
			"memory": 6000000000,
		},
		MaxMigrateRetry: 10,
		Cost:            &[]uint64{1200}[0],
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 保存配置（包含序列化）
		complexConfig.Name = fmt.Sprintf("complex-benchmark-config-%d", i)
		err := repo.SaveEngineQuotaConfig(ctx, complexConfig)
		if err != nil {
			b.Fatal(err)
		}

		// 获取配置（包含反序列化）
		_, err = repo.GetEngineQuotaConfig(ctx, complexConfig.Name)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkConcurrentAccess 基准测试并发访问
func BenchmarkConcurrentAccess(b *testing.B) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		b.Fatal(err)
	}

	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	if err != nil {
		b.Fatal(err)
	}

	// 插入测试数据
	testConfig := &MpsEngineQuotaConfig{
		Name:            "concurrent-config",
		Configs:         `{"engine":"concurrent","version":"1.0"}`,
		QuotaSet:        `{"cpu":1000,"gpu":0,"disk":1000}`,
		GmtCreate:       time.Now(),
		GmtModified:     time.Now(),
		MaxMigrateRetry: 5,
	}
	err = db.Create(testConfig).Error
	if err != nil {
		b.Fatal(err)
	}

	// 创建仓库
	dataLayer := &Data{
		db:  db,
		rdb: nil,
		log: log.NewHelper(log.DefaultLogger),
	}
	repo := NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := repo.GetEngineQuotaConfig(ctx, "concurrent-config")
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
