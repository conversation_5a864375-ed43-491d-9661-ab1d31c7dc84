package data

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"

	"mps-job-analysis-go/internal/conf"
)

// TestNewJobAnalysisRepo 测试创建 JobAnalysisRepo
func TestNewJobAnalysisRepo(t *testing.T) {
	// 创建模拟数据层
	data := &Data{
		db:  nil, // 在单元测试中可以为 nil
		rdb: nil,
		log: log.New<PERSON>elper(log.DefaultLogger),
	}

	// 创建仓库
	repo := NewJobAnalysisRepo(data, log.DefaultLogger)

	// 验证仓库不为 nil
	assert.NotNil(t, repo)
}

// TestNewExternalServiceRepo 测试创建 ExternalServiceRepo
func TestNewExternalServiceRepo(t *testing.T) {
	// 创建模拟配置
	conf := &conf.ExternalServices{
		Dmes: &conf.ExternalServices_DMES{
			Url:     "http://localhost:8081",
			Timeout: nil, // 可以为 nil
		},
		WorkerBrain: &conf.ExternalServices_WorkerBrain{
			MpsTranscodeNewUrl: "http://localhost:8082",
			MpsEditingUrl:      "http://localhost:8083",
			Timeout:            nil,
		},
		Sla: &conf.ExternalServices_SLA{
			Url:     "http://localhost:8084",
			Timeout: nil,
		},
		MediaMeta: &conf.ExternalServices_MediaMeta{
			Url:     "http://localhost:8085",
			Timeout: nil,
		},
	}

	// 创建仓库
	repo := NewExternalServiceRepo(conf, log.DefaultLogger)

	// 验证仓库不为 nil
	assert.NotNil(t, repo)
}
