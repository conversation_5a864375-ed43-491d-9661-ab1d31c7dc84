package data

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"mps-job-analysis-go/internal/biz"
)

// JobAnalysisTestSuite 作业分析数据层测试套件
type JobAnalysisTestSuite struct {
	suite.Suite
	db   *gorm.DB
	rdb  *redis.Client
	repo biz.JobAnalysisRepo
	ctx  context.Context
}

// SetupSuite 在所有测试开始前运行
func (suite *JobAnalysisTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 创建内存 SQLite 数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)

	suite.db = db

	// 自动迁移表结构
	err = db.AutoMigrate(&MpsEngineQuotaConfig{})
	require.NoError(suite.T(), err)

	// 创建模拟 Redis 客户端（可以为 nil，测试会处理）
	suite.rdb = nil

	// 创建数据访问层
	dataLayer := &Data{
		db:  suite.db,
		rdb: suite.rdb,
		log: log.NewHelper(log.DefaultLogger),
	}

	suite.repo = NewJobAnalysisRepo(dataLayer, log.DefaultLogger)

	// 插入测试数据
	suite.insertTestData()
}

// TearDownSuite 在所有测试结束后运行
func (suite *JobAnalysisTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, err := suite.db.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
}

// insertTestData 插入测试数据
func (suite *JobAnalysisTestSuite) insertTestData() {
	testConfigs := []MpsEngineQuotaConfig{
		{
			Name:            "analysis",
			Configs:         `{"jobType":"analysis"}`,
			QuotaSet:        `{"cpu":1000}`,
			Speed:           &[]float64{50.0}[0],
			DiskRatio:       &[]float64{1.0}[0],
			MaxMigrateRetry: 5,
		},
		{
			Name:            "editing",
			Configs:         `{"jobType":"editing"}`,
			QuotaSet:        `{"cpu":8000}`,
			DiskQuota:       &[]uint64{10000}[0],
			MaxMigrateRetry: 5,
			Cost:            &[]uint64{180}[0],
		},
		{
			Name:            "mps-transcode-new",
			Configs:         `{"engine":"mps-transcode-new","version":"1.0"}`,
			QuotaSet:        `{"cpu":1000,"gpu":0,"disk":1000}`,
			Cost:            &[]uint64{300}[0],
			Speed:           &[]float64{1.0}[0],
			DiskRatio:       &[]float64{1.0}[0],
			DiskQuota:       &[]uint64{1000}[0],
			MaxMigrateRetry: 5,
		},
		{
			Name:            "mps-editing",
			Configs:         `{"engine":"mps-editing","version":"1.0"}`,
			QuotaSet:        `{"cpu":2000,"gpu":1000,"disk":2000}`,
			Cost:            &[]uint64{600}[0],
			Speed:           &[]float64{0.5}[0],
			DiskRatio:       &[]float64{2.0}[0],
			DiskQuota:       &[]uint64{2000}[0],
			MaxMigrateRetry: 3,
		},
	}

	for _, config := range testConfigs {
		config.GmtCreate = time.Now()
		config.GmtModified = time.Now()
		err := suite.db.Create(&config).Error
		require.NoError(suite.T(), err)
	}
}

// TestGetEngineQuotaConfig 测试获取引擎配额配置
func (suite *JobAnalysisTestSuite) TestGetEngineQuotaConfig() {
	tests := []struct {
		name           string
		configName     string
		expectedConfig *biz.EngineQuotaConfig
		expectError    bool
	}{
		{
			name:       "get existing config - analysis",
			configName: "analysis",
			expectedConfig: &biz.EngineQuotaConfig{
				Name:    "analysis",
				Configs: `{"jobType":"analysis"}`,
				QuotaSet: map[string]int64{
					"cpu": 1000,
				},
				Speed:           &[]float64{50.0}[0],
				DiskRatio:       &[]float64{1.0}[0],
				MaxMigrateRetry: 5,
			},
			expectError: false,
		},
		{
			name:       "get existing config - editing",
			configName: "editing",
			expectedConfig: &biz.EngineQuotaConfig{
				Name:    "editing",
				Configs: `{"jobType":"editing"}`,
				QuotaSet: map[string]int64{
					"cpu": 8000,
				},
				DiskQuota:       &[]uint64{10000}[0],
				MaxMigrateRetry: 5,
				Cost:            &[]uint64{180}[0],
			},
			expectError: false,
		},
		{
			name:       "get existing config - mps-transcode-new",
			configName: "mps-transcode-new",
			expectedConfig: &biz.EngineQuotaConfig{
				Name:    "mps-transcode-new",
				Configs: `{"engine":"mps-transcode-new","version":"1.0"}`,
				QuotaSet: map[string]int64{
					"cpu":  1000,
					"gpu":  0,
					"disk": 1000,
				},
				Cost:            &[]uint64{300}[0],
				Speed:           &[]float64{1.0}[0],
				DiskRatio:       &[]float64{1.0}[0],
				DiskQuota:       &[]uint64{1000}[0],
				MaxMigrateRetry: 5,
			},
			expectError: false,
		},
		{
			name:           "get non-existing config",
			configName:     "non-existing",
			expectedConfig: nil,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			config, err := suite.repo.GetEngineQuotaConfig(suite.ctx, tt.configName)

			if tt.expectError {
				assert.Error(suite.T(), err)
			} else {
				assert.NoError(suite.T(), err)
			}

			if tt.expectedConfig == nil {
				assert.Nil(suite.T(), config)
			} else {
				require.NotNil(suite.T(), config)
				assert.Equal(suite.T(), tt.expectedConfig.Name, config.Name)
				assert.Equal(suite.T(), tt.expectedConfig.Configs, config.Configs)
				assert.Equal(suite.T(), tt.expectedConfig.QuotaSet, config.QuotaSet)
				assert.Equal(suite.T(), tt.expectedConfig.MaxMigrateRetry, config.MaxMigrateRetry)

				if tt.expectedConfig.Speed != nil {
					require.NotNil(suite.T(), config.Speed)
					assert.Equal(suite.T(), *tt.expectedConfig.Speed, *config.Speed)
				}
				if tt.expectedConfig.DiskRatio != nil {
					require.NotNil(suite.T(), config.DiskRatio)
					assert.Equal(suite.T(), *tt.expectedConfig.DiskRatio, *config.DiskRatio)
				}
				if tt.expectedConfig.DiskQuota != nil {
					require.NotNil(suite.T(), config.DiskQuota)
					assert.Equal(suite.T(), *tt.expectedConfig.DiskQuota, *config.DiskQuota)
				}
				if tt.expectedConfig.Cost != nil {
					require.NotNil(suite.T(), config.Cost)
					assert.Equal(suite.T(), *tt.expectedConfig.Cost, *config.Cost)
				}
			}
		})
	}
}

// TestSaveEngineQuotaConfig 测试保存引擎配额配置
func (suite *JobAnalysisTestSuite) TestSaveEngineQuotaConfig() {
	// 创建测试配置
	testConfig := &biz.EngineQuotaConfig{
		Name:    "test-config",
		Configs: `{"jobType":"test","testParam":"value"}`,
		QuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 500000,
		},
		Speed:                         &[]float64{1.5}[0],
		DiskRatio:                     &[]float64{2.0}[0],
		DiskQuota:                     &[]uint64{5000}[0],
		MaxMigrateRetry:               3,
		MigrateDiscardQuotaThreshold: map[string]int64{
			"cpu": 1500,
		},
		Cost: &[]uint64{120}[0],
	}

	// 保存配置
	err := suite.repo.SaveEngineQuotaConfig(suite.ctx, testConfig)
	assert.NoError(suite.T(), err)

	// 验证保存的配置
	savedConfig, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "test-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedConfig)

	assert.Equal(suite.T(), testConfig.Name, savedConfig.Name)
	assert.Equal(suite.T(), testConfig.Configs, savedConfig.Configs)
	assert.Equal(suite.T(), testConfig.QuotaSet, savedConfig.QuotaSet)
	assert.Equal(suite.T(), *testConfig.Speed, *savedConfig.Speed)
	assert.Equal(suite.T(), *testConfig.DiskRatio, *savedConfig.DiskRatio)
	assert.Equal(suite.T(), *testConfig.DiskQuota, *savedConfig.DiskQuota)
	assert.Equal(suite.T(), testConfig.MaxMigrateRetry, savedConfig.MaxMigrateRetry)
	assert.Equal(suite.T(), testConfig.MigrateDiscardQuotaThreshold, savedConfig.MigrateDiscardQuotaThreshold)
	assert.Equal(suite.T(), *testConfig.Cost, *savedConfig.Cost)

	// 清理测试数据
	suite.cleanupTestConfig("test-config")
}

// TestUpdateEngineQuotaConfig 测试更新引擎配额配置
func (suite *JobAnalysisTestSuite) TestUpdateEngineQuotaConfig() {
	// 首先创建一个配置
	originalConfig := &biz.EngineQuotaConfig{
		Name:    "update-test-config",
		Configs: `{"jobType":"update-test"}`,
		QuotaSet: map[string]int64{
			"cpu": 1000,
		},
		Speed:           &[]float64{1.0}[0],
		MaxMigrateRetry: 5,
	}

	err := suite.repo.SaveEngineQuotaConfig(suite.ctx, originalConfig)
	assert.NoError(suite.T(), err)

	// 获取保存后的配置（包含 ID）
	savedOriginal, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "update-test-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedOriginal)

	// 更新配置（使用相同的 ID）
	updatedConfig := &biz.EngineQuotaConfig{
		ID:      savedOriginal.ID, // 设置 ID 以便进行更新
		Name:    "update-test-config",
		Configs: `{"jobType":"update-test","updated":"true"}`,
		QuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		Speed:           &[]float64{2.0}[0],
		DiskRatio:       &[]float64{1.5}[0],
		MaxMigrateRetry: 3,
	}

	err = suite.repo.SaveEngineQuotaConfig(suite.ctx, updatedConfig)
	assert.NoError(suite.T(), err)

	// 验证更新的配置
	savedConfig, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "update-test-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedConfig)

	assert.Equal(suite.T(), updatedConfig.Configs, savedConfig.Configs)
	assert.Equal(suite.T(), updatedConfig.QuotaSet, savedConfig.QuotaSet)
	assert.Equal(suite.T(), *updatedConfig.Speed, *savedConfig.Speed)
	assert.Equal(suite.T(), *updatedConfig.DiskRatio, *savedConfig.DiskRatio)
	assert.Equal(suite.T(), updatedConfig.MaxMigrateRetry, savedConfig.MaxMigrateRetry)

	// 清理测试数据
	suite.cleanupTestConfig("update-test-config")
}

// cleanupTestConfig 清理测试配置
func (suite *JobAnalysisTestSuite) cleanupTestConfig(name string) {
	err := suite.db.Where("name = ?", name).Delete(&MpsEngineQuotaConfig{}).Error
	if err != nil {
		suite.T().Logf("Failed to cleanup test config %s: %v", name, err)
	}
}

// TestListEngineQuotaConfigs 测试列出引擎配额配置
func (suite *JobAnalysisTestSuite) TestListEngineQuotaConfigs() {
	// 测试分页查询
	configs, err := suite.repo.ListEngineQuotaConfigs(suite.ctx, 10, 0)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), configs)
	assert.LessOrEqual(suite.T(), len(configs), 10)

	// 验证返回的配置包含预期的数据
	foundAnalysis := false
	foundEditing := false
	foundTranscode := false
	for _, config := range configs {
		if config.Name == "analysis" {
			foundAnalysis = true
			assert.Equal(suite.T(), `{"jobType":"analysis"}`, config.Configs)
		}
		if config.Name == "editing" {
			foundEditing = true
			assert.Equal(suite.T(), `{"jobType":"editing"}`, config.Configs)
		}
		if config.Name == "mps-transcode-new" {
			foundTranscode = true
			assert.Equal(suite.T(), `{"engine":"mps-transcode-new","version":"1.0"}`, config.Configs)
		}
	}
	assert.True(suite.T(), foundAnalysis, "Should find analysis config")
	assert.True(suite.T(), foundEditing, "Should find editing config")
	assert.True(suite.T(), foundTranscode, "Should find mps-transcode-new config")

	// 测试偏移量
	configs2, err := suite.repo.ListEngineQuotaConfigs(suite.ctx, 2, 2)
	assert.NoError(suite.T(), err)
	assert.LessOrEqual(suite.T(), len(configs2), 2)

	// 测试限制数量
	configs3, err := suite.repo.ListEngineQuotaConfigs(suite.ctx, 1, 0)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, len(configs3))
}

// TestCacheOperations 测试缓存操作
func (suite *JobAnalysisTestSuite) TestCacheOperations() {
	// 由于我们没有真实的 Redis，这些操作应该优雅地处理 nil 情况

	// 测试获取不存在的缓存
	result, err := suite.repo.GetCachedResult(suite.ctx, "test-key")
	assert.Error(suite.T(), err) // 应该返回错误，因为 Redis 不可用
	assert.Nil(suite.T(), result)

	// 测试设置缓存
	testResult := &biz.JobAnalysisResult{
		Success: true,
		Code:    "Success",
		Message: "Test result",
		QuotaSet: map[string]int64{
			"cpu": 1000,
		},
	}

	err = suite.repo.SetCachedResult(suite.ctx, "test-key", testResult, time.Hour)
	assert.NoError(suite.T(), err) // 应该不返回错误，只是不执行操作
}

// TestGenerateCacheKey 测试缓存键生成
func (suite *JobAnalysisTestSuite) TestGenerateCacheKey() {
	param := &biz.JobAnalysisParam{
		EngineModel:  "mps-transcode-new",
		EngineParams: `{"format":"mp4","video":{"codec":"h264"}}`,
		UserID:       "test-user",
		JobID:        "test-job",
		AnalysisMode: "byParseEngineParams",
	}

	key := suite.repo.GenerateCacheKey(param)
	assert.NotEmpty(suite.T(), key)
	assert.Contains(suite.T(), key, "job_analysis")

	// 相同参数应该生成相同的键
	key2 := suite.repo.GenerateCacheKey(param)
	assert.Equal(suite.T(), key, key2)

	// 不同参数应该生成不同的键
	param2 := &biz.JobAnalysisParam{
		EngineModel:  "mps-editing",
		EngineParams: `{"type":"editing"}`,
		UserID:       "test-user",
		JobID:        "test-job",
		AnalysisMode: "byParseEngineParams",
	}

	key3 := suite.repo.GenerateCacheKey(param2)
	assert.NotEqual(suite.T(), key, key3)
}

// TestSaveJobResult 测试保存作业结果
func (suite *JobAnalysisTestSuite) TestSaveJobResult() {
	now := time.Now()
	param := &biz.JobExecutionResultReportParam{
		JobID: "test-job-result",
		AllocQuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		MaxQuotaSet: map[string]int64{
			"cpu": 2500,
			"gpu": 120000,
		},
		AvgQuotaSet: map[string]int64{
			"cpu": 1800,
			"gpu": 90000,
		},
		EngineModel:    "mps-transcode-new",
		EngineParams:   `{"format":"mp4","video":{"codec":"h264"}}`,
		ExpectCostTime: &[]int64{300}[0],
		RealCostTime:   &[]int64{280}[0],
		CreateTime:     &now,
		SubmitTime:     &now,
		PipelineID:     "test-pipeline",
		Tag:            "test",
		RequestID:      "test-request",
		Product:        "mps",
		UserID:         "test-user-123",
		Env:            "test",
		Station:        "test-station",
	}

	err := suite.repo.SaveJobResult(suite.ctx, param)
	assert.NoError(suite.T(), err) // 即使没有实际保存，也不应该返回错误
}

// TestErrorHandling 测试错误处理
func (suite *JobAnalysisTestSuite) TestErrorHandling() {
	// 测试获取不存在的配置
	config, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "non-existent-config")
	assert.NoError(suite.T(), err)
	assert.Nil(suite.T(), config)

	// 测试保存重复名称的配置（应该导致错误）
	duplicateConfig := &biz.EngineQuotaConfig{
		Name:    "analysis", // 使用已存在的名称
		Configs: `{"test": true}`,
		QuotaSet: map[string]int64{
			"cpu": 1000,
		},
	}

	err = suite.repo.SaveEngineQuotaConfig(suite.ctx, duplicateConfig)
	assert.Error(suite.T(), err) // 应该返回 UNIQUE constraint 错误
}

// TestComplexQuotaSet 测试复杂的配额集合
func (suite *JobAnalysisTestSuite) TestComplexQuotaSet() {
	complexConfig := &biz.EngineQuotaConfig{
		Name:    "complex-config",
		Configs: `{"engine":"complex","features":["gpu","cpu","memory"]}`,
		QuotaSet: map[string]int64{
			"cpu":     4000,
			"gpu":     2000000,
			"memory":  8000000000,
			"disk":    100000000000,
			"network": 1000000,
		},
		Speed:     &[]float64{0.75}[0],
		DiskRatio: &[]float64{3.5}[0],
		DiskQuota: &[]uint64{50000}[0],
		MigrateDiscardQuotaThreshold: map[string]int64{
			"cpu":    3000,
			"gpu":    1500000,
			"memory": 6000000000,
		},
		MaxMigrateRetry: 10,
		Cost:            &[]uint64{1200}[0],
	}

	// 保存复杂配置
	err := suite.repo.SaveEngineQuotaConfig(suite.ctx, complexConfig)
	assert.NoError(suite.T(), err)

	// 验证保存的配置
	savedConfig, err := suite.repo.GetEngineQuotaConfig(suite.ctx, "complex-config")
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), savedConfig)

	assert.Equal(suite.T(), complexConfig.Name, savedConfig.Name)
	assert.Equal(suite.T(), complexConfig.Configs, savedConfig.Configs)
	assert.Equal(suite.T(), complexConfig.QuotaSet, savedConfig.QuotaSet)
	assert.Equal(suite.T(), complexConfig.MigrateDiscardQuotaThreshold, savedConfig.MigrateDiscardQuotaThreshold)
	assert.Equal(suite.T(), *complexConfig.Speed, *savedConfig.Speed)
	assert.Equal(suite.T(), *complexConfig.DiskRatio, *savedConfig.DiskRatio)
	assert.Equal(suite.T(), *complexConfig.DiskQuota, *savedConfig.DiskQuota)
	assert.Equal(suite.T(), complexConfig.MaxMigrateRetry, savedConfig.MaxMigrateRetry)
	assert.Equal(suite.T(), *complexConfig.Cost, *savedConfig.Cost)

	// 清理测试数据
	suite.cleanupTestConfig("complex-config")
}

// TestJobAnalysisDataSuite 运行作业分析数据层测试套件
func TestJobAnalysisDataSuite(t *testing.T) {
	// 检查是否跳过集成测试
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping integration tests")
	}

	suite.Run(t, new(JobAnalysisTestSuite))
}
