package service

import (
	"context"
	pb "mps-job-analysis-go/api/job/v1"
	"mps-job-analysis-go/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

// JobAnalysisService 作业分析服务
type JobAnalysisService struct {
	pb.UnimplementedJobAnalysisServiceServer
	pb.UnimplementedServiceHealthServiceServer

	uc  biz.JobAnalysisUsecase
	log *log.Helper
}

// NewJobAnalysisService 创建作业分析服务
func NewJobAnalysisService(uc biz.JobAnalysisUsecase, logger log.Logger) *JobAnalysisService {
	return &JobAnalysisService{
		uc:  uc,
		log: log.<PERSON>elper(logger),
	}
}

// AnalyzeJob 分析作业
func (s *JobAnalysisService) AnalyzeJob(ctx context.Context, req *pb.AnalyzeJobRequest) (*pb.AnalyzeJobResponse, error) {
	s.log.WithContext(ctx).Infof("analyzing job: %s", req.JobId)

	// 转换请求参数
	param := &biz.JobAnalysisParam{
		EngineModel:          req.EngineModel,
		EngineParams:         req.EngineParams,
		UserID:               req.UserId,
		JobID:                req.JobId,
		SliceProcess:         req.SliceProcess,
		AnalysisMode:         req.AnalysisMode,
		Tag:                  req.Tag,
		MaxSliceNum:          &req.MaxSliceNum,
		MinSliceDuration:     &req.MinSliceDuration,
		UseWorkerBrainResult: req.UseWorkerBrainResult,
		InvokeWorkerBrain:    req.InvokeWorkerBrain,
		SpeedXRange:          req.SpeedXRange,
		IsAutoSpeedX:         req.IsAutoSpeedX,
		PipelineID:           req.PipelineId,
		RequestID:            req.RequestId,
	}

	if req.CreateTime != nil {
		createTime := req.CreateTime.AsTime()
		param.CreateTime = &createTime
	}

	// 转换调度参数
	if req.ScheduleParams != nil {
		param.ScheduleParams = &biz.ScheduleParams{
			PipelineID:     req.ScheduleParams.PipelineId,
			QuotaSet:       req.ScheduleParams.QuotaSet,
			Priority:       &req.ScheduleParams.Priority,
			ExpectCostTime: &req.ScheduleParams.ExpectCostTime,
			ParallelNum:    &req.ScheduleParams.ParallelNum,
			SlaLevel:       req.ScheduleParams.SlaLevel,
			SliceNum:       &req.ScheduleParams.SliceNum,
			SpeedXRange:    req.ScheduleParams.SpeedXRange,
		}

		// 转换输入参数
		for _, input := range req.ScheduleParams.Inputs {
			param.ScheduleParams.Inputs = append(param.ScheduleParams.Inputs, biz.Input{
				Duration:     input.Duration,
				VideoBitrate: input.VideoBitrate,
				AvgFps:       input.AvgFps,
				Size:         input.Size,
				Format:       input.Format,
				Fps:          input.Fps,
				Width:        input.Width,
				AudioCodec:   input.AudioCodec,
				AudioBitrate: input.AudioBitrate,
				Height:       input.Height,
				VideoCodec:   input.VideoCodec,
			})
		}

		// 转换配置参数
		for _, config := range req.ScheduleParams.Configs {
			bizConfig := biz.Config{
				Duration:     config.Duration,
				NhVersion:    config.NhVersion,
				Format:       config.Format,
				Fps:          config.Fps,
				ID:           config.Id,
				AudioCodec:   config.AudioCodec,
				JobType:      config.JobType,
				TemplateID:   config.TemplateId,
				AudioBitrate: config.AudioBitrate,
				VideoCodec:   config.VideoCodec,
				AudioOnly:    config.AudioOnly,
				Extend:       config.Extend,
				GpuRestore:   config.GpuRestore,
			}
			if config.ByWorkerBrain {
				bizConfig.ByWorkerBrain = &config.ByWorkerBrain
			}
			param.ScheduleParams.Configs = append(param.ScheduleParams.Configs, bizConfig)
		}
	}

	// 调用业务逻辑
	result, err := s.uc.AnalyzeJob(ctx, param)
	if err != nil {
		s.log.WithContext(ctx).Errorf("failed to analyze job: %v", err)
		return &pb.AnalyzeJobResponse{
			Success: false,
			Code:    "InternalError",
			Message: err.Error(),
		}, nil
	}

	// 转换响应
	resp := &pb.AnalyzeJobResponse{
		Success:                      result.Success,
		Code:                         result.Code,
		Message:                      result.Message,
		ExecuteMode:                  convertExecuteMode(result.ExecuteMode),
		QuotaSet:                     result.QuotaSet,
		OriginQuotaSet:               result.OriginQuotaSet,
		MigrateDiscardQuotaThreshold: result.MigrateDiscardQuotaThreshold,
	}

	if result.ExpectCostTime != nil {
		resp.ExpectCostTime = *result.ExpectCostTime
	}

	if result.SlaFinishDelay != nil {
		resp.SlaFinishDelay = *result.SlaFinishDelay
	}

	if result.SlaQueuingDelay != nil {
		resp.SlaQueuingDelay = *result.SlaQueuingDelay
	}

	if result.MaxMigrateRetry != nil {
		resp.MaxMigrateRetry = *result.MaxMigrateRetry
	}

	// 转换DAG图
	if result.Graph != nil {
		resp.Graph = convertDagJobGraph(result.Graph)
	}

	// 转换图集合
	if result.GraphMap != nil {
		resp.GraphMap = make(map[string]*pb.DagJobGraph)
		for k, v := range result.GraphMap {
			resp.GraphMap[k] = convertDagJobGraph(v)
		}
	}

	// 转换倍速消息
	if result.SpeedXMessage != nil {
		resp.SpeedXMessage = make(map[string]*pb.SpeedXMessage)
		for k, v := range result.SpeedXMessage {
			resp.SpeedXMessage[k] = &pb.SpeedXMessage{
				Code:    v.Code,
				Message: v.Message,
			}
		}
	}

	return resp, nil
}

// ReportResult 报告结果
func (s *JobAnalysisService) ReportResult(ctx context.Context, req *pb.ReportResultRequest) (*pb.ReportResultResponse, error) {
	s.log.WithContext(ctx).Infof("reporting result for job: %s", req.JobId)

	// 转换请求参数
	param := &biz.JobExecutionResultReportParam{
		JobID:          req.JobId,
		AllocQuotaSet:  req.AllocQuotaSet,
		MaxQuotaSet:    req.MaxQuotaSet,
		AvgQuotaSet:    req.AvgQuotaSet,
		EngineModel:    req.EngineModel,
		EngineParams:   req.EngineParams,
		ExpectCostTime: &req.ExpectCostTime,
		RealCostTime:   &req.RealCostTime,
		PipelineID:     req.PipelineId,
		Tag:            req.Tag,
		RequestID:      req.RequestId,
		Product:        req.Product,
		UserID:         req.UserId,
		Env:            req.Env,
		Station:        req.Station,
	}

	if req.CreateTime != nil {
		createTime := req.CreateTime.AsTime()
		param.CreateTime = &createTime
	}

	if req.SubmitTime != nil {
		submitTime := req.SubmitTime.AsTime()
		param.SubmitTime = &submitTime
	}

	// 调用业务逻辑
	if err := s.uc.ReportResult(ctx, param); err != nil {
		s.log.WithContext(ctx).Errorf("failed to report result: %v", err)
		return &pb.ReportResultResponse{
			Code:    "InternalError",
			Message: err.Error(),
		}, nil
	}

	return &pb.ReportResultResponse{
		Code:    "Success",
		Message: "Result reported successfully",
	}, nil
}

// CheckLiveness 存活性检查
func (s *JobAnalysisService) CheckLiveness(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	return &pb.HealthCheckResponse{
		Code:    "Success",
		Message: "Service is alive",
	}, nil
}

// CheckReadiness 就绪性检查
func (s *JobAnalysisService) CheckReadiness(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	if err := s.uc.CheckHealth(ctx); err != nil {
		s.log.WithContext(ctx).Errorf("readiness check failed: %v", err)
		return &pb.HealthCheckResponse{
			Code:    "NotReady",
			Message: "Service is not ready",
		}, nil
	}

	return &pb.HealthCheckResponse{
		Code:    "Success",
		Message: "Service is ready",
	}, nil
}

// 辅助函数
func convertExecuteMode(mode biz.ExecuteMode) pb.ExecuteMode {
	switch mode {
	case biz.ExecuteModeProbeFirst:
		return pb.ExecuteMode_PROBE_FIRST
	case biz.ExecuteModeSingle:
		return pb.ExecuteMode_SINGLE
	case biz.ExecuteModeDAG:
		return pb.ExecuteMode_DAG
	default:
		return pb.ExecuteMode_SINGLE
	}
}

func convertDagJobGraph(graph *biz.DagJobGraph) *pb.DagJobGraph {
	if graph == nil {
		return nil
	}

	pbGraph := &pb.DagJobGraph{}

	// 转换顶点
	for _, vertex := range graph.JobVertexs {
		pbVertex := &pb.JobVertex{
			Name:         vertex.Name,
			EngineModel:  vertex.EngineModel,
			EngineParams: vertex.EngineParams,
		}

		if vertex.ResourceRequest != nil {
			pbVertex.ResourceRequest = &pb.TaskResourceRequest{
				ExpectCostTime: vertex.ResourceRequest.ExpectCostTime,
				QuotaSet:       vertex.ResourceRequest.QuotaSet,
			}
		}

		if vertex.MaxMigrateRetry != nil {
			pbVertex.MaxMigrateRetry = *vertex.MaxMigrateRetry
		}

		pbGraph.JobVertexs = append(pbGraph.JobVertexs, pbVertex)
	}

	// 转换边
	for _, edge := range graph.JobEdges {
		pbEdge := &pb.JobEdge{
			Name:       edge.Name,
			From:       edge.From,
			To:         edge.To,
			Mode:       edge.Mode,
			Status:     edge.Status,
			FromStatus: edge.FromStatus,
		}
		pbGraph.JobEdges = append(pbGraph.JobEdges, pbEdge)
	}

	return pbGraph
}
