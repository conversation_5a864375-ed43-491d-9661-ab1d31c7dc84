package biz

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"mps-job-analysis-go/internal/data"
)

// BizIntegrationTestSuite 业务逻辑集成测试套件
type BizIntegrationTestSuite struct {
	suite.Suite
	mysqlContainer testcontainers.Container
	db             *gorm.DB
	usecase        *JobAnalysisUsecase
	ctx            context.Context
}

// SetupSuite 在所有测试开始前运行
func (suite *BizIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 启动 MySQL 容器
	mysqlContainer, err := mysql.Run(suite.ctx,
		"mysql:8.0",
		mysql.WithDatabase("testdb"),
		mysql.WithUsername("testuser"),
		mysql.WithPassword("testpass"),
		mysql.WithScripts(filepath.Join("..", "..", "scripts", "data.sql")),
	)
	require.NoError(suite.T(), err)

	suite.mysqlContainer = mysqlContainer

	// 获取连接信息
	host, err := mysqlContainer.Host(suite.ctx)
	require.NoError(suite.T(), err)

	port, err := mysqlContainer.MappedPort(suite.ctx, "3306")
	require.NoError(suite.T(), err)

	// 创建数据库连接
	dsn := fmt.Sprintf("testuser:testpass@tcp(%s:%s)/testdb?charset=utf8mb4&parseTime=True&loc=Local",
		host, port.Port())

	suite.db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)

	// 创建数据访问层
	dataLayer := &data.Data{
		DB:  suite.db,
		RDB: nil, // Redis 在这个测试中不需要
		Log: log.NewHelper(log.DefaultLogger),
	}

	// 创建仓库
	jobAnalysisRepo := data.NewJobAnalysisRepo(dataLayer, log.DefaultLogger)
	
	// 创建模拟的外部服务仓库
	externalServiceRepo := &MockExternalServiceRepo{}

	// 创建业务逻辑层
	suite.usecase = NewJobAnalysisUsecase(jobAnalysisRepo, externalServiceRepo, log.DefaultLogger)
}

// TearDownSuite 在所有测试结束后运行
func (suite *BizIntegrationTestSuite) TearDownSuite() {
	if suite.mysqlContainer != nil {
		err := suite.mysqlContainer.Terminate(suite.ctx)
		require.NoError(suite.T(), err)
	}
}

// MockExternalServiceRepo 模拟外部服务仓库
type MockExternalServiceRepo struct{}

func (m *MockExternalServiceRepo) CallWorkerBrain(ctx context.Context, param *WorkerBrainParam) (*WorkerBrainResult, error) {
	// 模拟 WorkerBrain 响应
	return &WorkerBrainResult{
		Success: true,
		QuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		ExpectCostTime: 300,
	}, nil
}

func (m *MockExternalServiceRepo) CallSLA(ctx context.Context, param *SLAParam) (*SLAResult, error) {
	// 模拟 SLA 响应
	return &SLAResult{
		Success:           true,
		SLAFinishDelay:    60,
		SLAQueuingDelay:   30,
		MaxMigrateRetry:   5,
	}, nil
}

func (m *MockExternalServiceRepo) CallDMES(ctx context.Context, param *DMESParam) (*DMESResult, error) {
	// 模拟 DMES 响应
	return &DMESResult{
		Success: true,
		QuotaSet: map[string]int64{
			"cpu":  1500,
			"disk": 2000,
		},
	}, nil
}

func (m *MockExternalServiceRepo) CallMediaMeta(ctx context.Context, param *MediaMetaParam) (*MediaMetaResult, error) {
	// 模拟 MediaMeta 响应
	return &MediaMetaResult{
		Success:  true,
		Duration: 120.5,
		Width:    1920,
		Height:   1080,
		Bitrate:  5000000,
	}, nil
}

// TestAnalyzeJobByParseEngineParams 测试通过解析引擎参数进行作业分析
func (suite *BizIntegrationTestSuite) TestAnalyzeJobByParseEngineParams() {
	param := &JobAnalysisParam{
		EngineModel:  "mps-transcode-new",
		EngineParams: `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"},"audio":{"codec":"aac","bitrate":"128k"}}`,
		UserID:       "test-user-123",
		JobID:        "test-job-456",
		AnalysisMode: "byParseEngineParams",
		Tag:          "test",
		PipelineID:   "test-pipeline",
		RequestID:    "test-request",
	}

	result, err := suite.usecase.AnalyzeJob(suite.ctx, param)
	
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), result)
	
	assert.True(suite.T(), result.Success)
	assert.Equal(suite.T(), "Success", result.Code)
	assert.Equal(suite.T(), "Analysis completed successfully", result.Message)
	assert.Equal(suite.T(), ExecuteModeSingle, result.ExecuteMode)
	
	// 验证配额设置
	assert.NotEmpty(suite.T(), result.QuotaSet)
	assert.Contains(suite.T(), result.QuotaSet, "cpu")
	assert.Contains(suite.T(), result.QuotaSet, "disk")
	assert.Contains(suite.T(), result.QuotaSet, "gpu")
	
	// 验证期望耗时
	assert.NotNil(suite.T(), result.ExpectCostTime)
	assert.Greater(suite.T(), *result.ExpectCostTime, int64(0))
}

// TestAnalyzeJobWithDifferentEngineModels 测试不同引擎模型的作业分析
func (suite *BizIntegrationTestSuite) TestAnalyzeJobWithDifferentEngineModels() {
	testCases := []struct {
		name         string
		engineModel  string
		engineParams string
		expectError  bool
	}{
		{
			name:         "transcode engine",
			engineModel:  "mps-transcode-new",
			engineParams: `{"format":"mp4","video":{"codec":"h264","width":1920,"height":1080}}`,
			expectError:  false,
		},
		{
			name:         "editing engine",
			engineModel:  "mps-editing",
			engineParams: `{"type":"editing","effects":["fade","crop"]}`,
			expectError:  false,
		},
		{
			name:         "analysis engine",
			engineModel:  "mps-analysis",
			engineParams: `{"type":"analysis","features":["scene_detection"]}`,
			expectError:  false,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			param := &JobAnalysisParam{
				EngineModel:  tc.engineModel,
				EngineParams: tc.engineParams,
				UserID:       "test-user-123",
				JobID:        fmt.Sprintf("test-job-%s", tc.name),
				AnalysisMode: "byParseEngineParams",
				Tag:          "test",
				PipelineID:   "test-pipeline",
				RequestID:    "test-request",
			}

			result, err := suite.usecase.AnalyzeJob(suite.ctx, param)

			if tc.expectError {
				assert.Error(suite.T(), err)
			} else {
				assert.NoError(suite.T(), err)
				require.NotNil(suite.T(), result)
				assert.True(suite.T(), result.Success)
			}
		})
	}
}

// TestAnalyzeJobWithSliceProcess 测试切片处理的作业分析
func (suite *BizIntegrationTestSuite) TestAnalyzeJobWithSliceProcess() {
	param := &JobAnalysisParam{
		EngineModel:      "mps-transcode-new",
		EngineParams:     `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"}}`,
		UserID:           "test-user-123",
		JobID:            "test-job-slice",
		AnalysisMode:     "byParseEngineParams",
		SliceProcess:     true,
		MaxSliceNum:      &[]int32{10}[0],
		MinSliceDuration: &[]int32{30}[0],
		Tag:              "test",
		PipelineID:       "test-pipeline",
		RequestID:        "test-request",
	}

	result, err := suite.usecase.AnalyzeJob(suite.ctx, param)
	
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), result)
	
	assert.True(suite.T(), result.Success)
	assert.Equal(suite.T(), ExecuteModeSingle, result.ExecuteMode)
	
	// 对于切片处理，应该有特定的配额计算
	assert.NotEmpty(suite.T(), result.QuotaSet)
}

// TestAnalyzeJobWithWorkerBrain 测试使用 WorkerBrain 的作业分析
func (suite *BizIntegrationTestSuite) TestAnalyzeJobWithWorkerBrain() {
	param := &JobAnalysisParam{
		EngineModel:          "mps-transcode-new",
		EngineParams:         `{"format":"mp4","video":{"codec":"h264","bitrate":"1000k"}}`,
		UserID:               "test-user-123",
		JobID:                "test-job-worker-brain",
		AnalysisMode:         "byParseEngineParams",
		InvokeWorkerBrain:    true,
		UseWorkerBrainResult: true,
		Tag:                  "test",
		PipelineID:           "test-pipeline",
		RequestID:            "test-request",
	}

	result, err := suite.usecase.AnalyzeJob(suite.ctx, param)
	
	assert.NoError(suite.T(), err)
	require.NotNil(suite.T(), result)
	
	assert.True(suite.T(), result.Success)
	
	// 当使用 WorkerBrain 结果时，应该使用 WorkerBrain 返回的配额
	assert.NotEmpty(suite.T(), result.QuotaSet)
	// 验证是否使用了 MockExternalServiceRepo 返回的值
	assert.Equal(suite.T(), int64(2000), result.QuotaSet["cpu"])
	assert.Equal(suite.T(), int64(100000), result.QuotaSet["gpu"])
}

// TestReportResult 测试结果报告
func (suite *BizIntegrationTestSuite) TestReportResult() {
	now := time.Now()
	param := &JobExecutionResultReportParam{
		JobID: "test-job-report",
		AllocQuotaSet: map[string]int64{
			"cpu": 2000,
			"gpu": 100000,
		},
		MaxQuotaSet: map[string]int64{
			"cpu": 2500,
			"gpu": 120000,
		},
		AvgQuotaSet: map[string]int64{
			"cpu": 1800,
			"gpu": 90000,
		},
		EngineModel:    "mps-transcode-new",
		EngineParams:   `{"format":"mp4","video":{"codec":"h264"}}`,
		ExpectCostTime: &[]int64{300}[0],
		RealCostTime:   &[]int64{280}[0],
		CreateTime:     &now,
		SubmitTime:     &now,
		PipelineID:     "test-pipeline",
		Tag:            "test",
		RequestID:      "test-request",
		Product:        "mps",
		UserID:         "test-user-123",
		Env:            "test",
		Station:        "test-station",
	}

	err := suite.usecase.ReportResult(suite.ctx, param)
	
	assert.NoError(suite.T(), err)
}

// TestCheckHealth 测试健康检查
func (suite *BizIntegrationTestSuite) TestCheckHealth() {
	err := suite.usecase.CheckHealth(suite.ctx)
	
	// 在这个测试中，数据库是可用的，所以健康检查应该通过
	assert.NoError(suite.T(), err)
}

// TestAnalyzeJobWithInvalidParams 测试无效参数的作业分析
func (suite *BizIntegrationTestSuite) TestAnalyzeJobWithInvalidParams() {
	testCases := []struct {
		name  string
		param *JobAnalysisParam
	}{
		{
			name: "empty engine model",
			param: &JobAnalysisParam{
				EngineModel:  "",
				EngineParams: `{"format":"mp4"}`,
				UserID:       "test-user",
				JobID:        "test-job",
				AnalysisMode: "byParseEngineParams",
			},
		},
		{
			name: "empty engine params",
			param: &JobAnalysisParam{
				EngineModel:  "mps-transcode-new",
				EngineParams: "",
				UserID:       "test-user",
				JobID:        "test-job",
				AnalysisMode: "byParseEngineParams",
			},
		},
		{
			name: "invalid JSON in engine params",
			param: &JobAnalysisParam{
				EngineModel:  "mps-transcode-new",
				EngineParams: `{"format":"mp4"`,
				UserID:       "test-user",
				JobID:        "test-job",
				AnalysisMode: "byParseEngineParams",
			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			result, err := suite.usecase.AnalyzeJob(suite.ctx, tc.param)
			
			// 应该返回错误或者失败的结果
			if err == nil {
				require.NotNil(suite.T(), result)
				assert.False(suite.T(), result.Success)
			} else {
				assert.Error(suite.T(), err)
			}
		})
	}
}

// TestBizIntegrationSuite 运行业务逻辑集成测试套件
func TestBizIntegrationSuite(t *testing.T) {
	// 检查是否在 CI 环境中或者有 Docker
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping integration tests")
	}

	suite.Run(t, new(BizIntegrationTestSuite))
}
