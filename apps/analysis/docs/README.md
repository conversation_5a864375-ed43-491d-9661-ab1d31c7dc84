# MPS Job Analysis Go

基于 go-kratos 框架的媒体处理服务作业分析系统的 Go 语言重写版本。

## 项目概述

这是一个完整的微服务项目，从 Java 版本的 MPS Job Analysis 系统重写而来，使用 go-kratos 框架实现。系统提供作业分析、资源配额管理、DAG 图生成等核心功能。

## 技术栈

- **框架**: go-kratos v2.7.2
- **数据库**: MySQL + GORM
- **缓存**: Redis
- **API**: gRPC + HTTP (通过 grpc-gateway)
- **依赖注入**: Google Wire
- **配置管理**: YAML + Protobuf
- **容器化**: Docker + Docker Compose

## 项目结构

```
mps-job-analysis-go/
├── api/                    # API 定义 (protobuf)
│   └── job/v1/
├── cmd/server/             # 应用入口
├── configs/                # 配置文件
├── internal/               # 内部代码
│   ├── biz/               # 业务逻辑层
│   ├── data/              # 数据访问层
│   ├── service/           # 服务层
│   ├── server/            # 服务器配置
│   └── conf/              # 配置结构
├── third_party/           # 第三方 proto 文件
├── scripts/               # 脚本文件
├── Dockerfile             # Docker 构建文件
├── docker-compose.yml     # 开发环境
└── Makefile              # 构建脚本
```

## 核心功能

### 1. 作业分析
- **byParseEngineParams**: 基于引擎参数分析
- **byProbeMeta**: 基于探测元数据分析（已废弃）
- **byScheduleParams**: 基于调度参数分析

### 2. 资源管理
- 引擎配额配置管理
- CPU/GPU/磁盘资源估算
- 资源使用情况监控

### 3. 外部服务集成
- DMES 服务调用
- WorkerBrain 服务调用
- SLA 服务调用
- MediaMeta 服务调用

### 4. DAG 图生成
- 作业依赖关系分析
- 并行执行计划生成
- 资源分配优化

## 快速开始

### 1. 环境要求
- Go 1.21+
- MySQL 5.7+
- Redis 6.0+
- Docker (可选)

### 2. 编译项目
```bash
# 安装依赖
go mod tidy

# 生成代码
make api
make config
make wire

# 编译
make build
```

### 3. 配置数据库
```bash
# 初始化数据库
mysql -u root -p < scripts/init_db.sql
```

### 4. 启动服务
```bash
# 直接运行
./bin/server -conf ./configs/config.yaml

# 或使用 Docker Compose
docker-compose up -d
```

### 5. 测试 API
```bash
# 运行测试脚本
./test_api.sh

# 或手动测试
curl -X GET http://localhost:8000/health/liveness
```

## API 文档

### 作业分析
```bash
POST /v1/job/analyze
Content-Type: application/json

{
  "job_id": "test-job-001",
  "engine_model": "ffmpeg",
  "analysis_mode": "byParseEngineParams",
  "parse_engine_params": {
    "input_url": "http://example.com/video.mp4",
    "output_format": "mp4",
    "resolution": "1920x1080"
  }
}
```

### 结果报告
```bash
POST /v1/job/report
Content-Type: application/json

{
  "job_id": "test-job-001",
  "status": "SUCCESS",
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-01T01:00:00Z",
  "resource_usage": {
    "cpu_usage": 80.5,
    "memory_usage": 1024,
    "disk_usage": 2048
  }
}
```

### 健康检查
```bash
GET /health/liveness    # 存活检查
GET /health/readiness   # 就绪检查
```

## 开发指南

### 添加新的 API
1. 在 `api/job/v1/job.proto` 中定义新的服务和消息
2. 运行 `make api` 生成代码
3. 在 `internal/service/` 中实现服务逻辑
4. 在 `internal/biz/` 中实现业务逻辑
5. 在 `internal/data/` 中实现数据访问

### 运行测试
```bash
go test ./...
```

### 代码生成
```bash
make api      # 生成 API 代码
make config   # 生成配置代码
make wire     # 生成依赖注入代码
```

## 部署

### Docker 部署
```bash
# 构建镜像
docker build -t mps-job-analysis-go .

# 运行容器
docker run -p 8000:8000 -p 9000:9000 mps-job-analysis-go
```

### Kubernetes 部署
参考 `deployments/` 目录中的 YAML 文件。

## 监控和日志

- **日志**: 使用 kratos 内置日志系统
- **指标**: 支持 Prometheus 指标收集
- **链路追踪**: 支持 OpenTelemetry

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请提交 Issue 或联系维护者。
