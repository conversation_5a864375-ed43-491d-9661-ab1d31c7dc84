# Testing Guide

本文档描述了如何运行 MPS Job Analysis 服务的各种测试。

## 测试类型

### 1. 单元测试

单元测试测试单个函数或方法的功能，不依赖外部服务。

```bash
# 运行所有单元测试
make test

# 运行特定包的单元测试
go test -v ./internal/biz
go test -v ./internal/service

# 运行单元测试并生成覆盖率报告
go test -v -cover ./...
```

### 2. 集成测试

集成测试使用真实的 MySQL 数据库测试完整的功能流程。这些测试使用 `testcontainers-go` 自动启动和管理 MySQL 容器。

```bash
# 运行所有集成测试
make test-integration

# 运行集成测试并生成 HTML 覆盖率报告
make test-integration-html

# 运行所有测试（单元测试 + 集成测试）
make test-all
```

### 3. 端到端测试

端到端测试启动完整的 HTTP 服务器并测试 API 端点。

```bash
# 端到端测试包含在集成测试中
cd test && go test -v -tags=integration
```

## 测试结构

### 数据层测试 (`internal/data/integration_test.go`)

测试数据访问层的功能：
- 数据库连接和查询
- 引擎配额配置的 CRUD 操作
- 缓存操作（Redis）

### 业务逻辑层测试 (`internal/biz/integration_test.go`)

测试业务逻辑层的功能：
- 作业分析算法
- 外部服务集成
- 配额计算
- 错误处理

### 端到端测试 (`test/e2e_test.go`)

测试完整的 HTTP API：
- `/job/analysis` 接口
- `/job/reportResult` 接口
- `/service/liveness` 和 `/service/readiness` 健康检查
- 错误处理和验证

## 测试数据

测试使用 `scripts/data.sql` 中的数据，包含：
- 各种引擎配额配置
- 不同类型的转码配置
- 编辑和分析配置

## 环境要求

### 本地运行

1. **Docker**: 集成测试需要 Docker 来运行 MySQL 容器
2. **Go 1.23+**: 运行测试需要 Go 1.23 或更高版本

```bash
# 检查 Docker 是否可用
docker --version
docker info

# 检查 Go 版本
go version
```

### CI/CD 环境

在 CI/CD 环境中，可以设置环境变量来跳过集成测试：

```bash
export SKIP_INTEGRATION_TESTS=true
```

## 运行测试

### 快速开始

```bash
# 1. 确保 Docker 正在运行
docker info

# 2. 进入项目目录
cd apps/analysis

# 3. 运行所有测试
make test-all
```

### 详细步骤

1. **准备环境**
   ```bash
   # 安装依赖
   go mod download
   
   # 确保 Docker 运行
   docker info
   ```

2. **运行单元测试**
   ```bash
   make test
   ```

3. **运行集成测试**
   ```bash
   make test-integration
   ```

4. **查看覆盖率报告**
   ```bash
   make test-integration-html
   open coverage.html
   ```

### Docker 环境中运行

如果本地环境有问题，可以在 Docker 容器中运行测试：

```bash
make docker-test
```

## 测试配置

### 环境变量

- `SKIP_INTEGRATION_TESTS=true`: 跳过集成测试
- `CGO_ENABLED=1`: 启用 CGO（SQLite 驱动需要）
- `GO111MODULE=on`: 启用 Go 模块

### 测试标签

使用 Go 构建标签来控制测试：

```bash
# 只运行集成测试
go test -v -tags=integration ./...

# 排除集成测试
go test -v -tags=!integration ./...
```

## 故障排除

### 常见问题

1. **Docker 连接失败**
   ```
   Error: Cannot connect to the Docker daemon
   ```
   解决方案：确保 Docker 守护进程正在运行

2. **端口冲突**
   ```
   Error: Port already in use
   ```
   解决方案：testcontainers 会自动分配随机端口，通常不会有冲突

3. **MySQL 容器启动失败**
   ```
   Error: Failed to start MySQL container
   ```
   解决方案：检查 Docker 资源限制，确保有足够的内存

4. **测试超时**
   ```
   Error: Test timeout
   ```
   解决方案：增加测试超时时间或检查网络连接

### 调试技巧

1. **查看容器日志**
   ```bash
   # 在测试失败时，容器可能仍在运行
   docker ps
   docker logs <container_id>
   ```

2. **保留测试容器**
   ```bash
   # 修改测试代码，在 TearDownSuite 中不删除容器
   # 这样可以手动检查数据库状态
   ```

3. **增加日志级别**
   ```bash
   # 在测试中设置更详细的日志
   export LOG_LEVEL=debug
   ```

## 性能测试

### 基准测试

```bash
# 运行基准测试
go test -bench=. -benchmem ./...

# 运行特定的基准测试
go test -bench=BenchmarkAnalyzeJob -benchmem ./internal/biz
```

### 负载测试

可以使用工具如 `hey` 或 `ab` 进行负载测试：

```bash
# 安装 hey
go install github.com/rakyll/hey@latest

# 运行负载测试
hey -n 1000 -c 10 -m POST -H "Content-Type: application/json" \
  -d '{"engine_model":"mps-transcode-new","engine_params":"{}","user_id":"test","job_id":"test","analysis_mode":"byParseEngineParams"}' \
  http://localhost:8080/job/analysis
```

## 持续集成

项目配置了 GitHub Actions 来自动运行测试：

- `.github/workflows/integration-tests.yml`: 运行集成测试
- 测试在每次 push 和 pull request 时自动运行
- 覆盖率报告自动上传到 Codecov

## 贡献指南

在提交代码时，请确保：

1. 所有测试都通过
2. 新功能有相应的测试
3. 测试覆盖率不降低
4. 遵循现有的测试模式和约定

```bash
# 提交前检查
make test-all
```
