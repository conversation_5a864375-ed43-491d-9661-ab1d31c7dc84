# Analysis Service 阻塞问题排查和解决方案

## 问题描述

`/job/analysis` 接口调用时出现阻塞，无法正常响应请求。

## 问题分析

通过排查发现以下几个导致阻塞的问题：

### 1. 数据库连接失败导致服务启动失败

**问题**：在 `apps/analysis/internal/data/data.go` 中，当数据库连接失败时，代码调用 `log.Fatalf()` 导致程序直接退出。

**原因**：
```go
if err != nil {
    log.Fatalf("failed opening connection to mysql: %v", err)  // 这里会导致程序退出
}
```

**解决方案**：
- 将 `log.Fatalf()` 改为 `log.Errorf()`
- 允许服务在没有数据库连接的情况下继续运行
- 在数据访问层添加 nil 检查

### 2. 数据访问层没有处理数据库为 nil 的情况

**问题**：当数据库连接为 nil 时，数据访问方法会 panic。

**解决方案**：
在所有数据访问方法中添加 nil 检查：
```go
func (r *jobAnalysisRepo) GetEngineQuotaConfig(ctx context.Context, name string) (*biz.EngineQuotaConfig, error) {
    // 如果数据库连接不可用，返回 nil（使用默认配置）
    if r.data.db == nil {
        r.log.Warn("database not available, returning nil config")
        return nil, nil
    }
    // ... 原有逻辑
}
```

### 3. 外部服务调用超时时间过长

**问题**：HTTP 客户端默认超时时间为 30 秒，可能导致长时间阻塞。

**解决方案**：
- 将 HTTP 客户端超时时间从 30 秒减少到 5 秒
- 在每个请求中添加额外的超时控制
- 增加更详细的错误日志

### 4. 服务注册失败

**问题**：Nacos 服务注册失败可能导致启动阻塞。

**解决方案**：
在测试配置中禁用服务注册：
```yaml
app:
  # registry:
  #   endpoint: "http://localhost:8848"
```

### 5. 资源清理时的 panic

**问题**：在 cleanup 函数中，当 db 为 nil 时调用 `db.DB()` 会导致 panic。

**解决方案**：
```go
cleanup := func() {
    log.NewHelper(logger).Info("closing the data resources")
    if db != nil {  // 添加 nil 检查
        if sqlDB, err := db.DB(); err == nil {
            sqlDB.Close()
        }
    }
    if rdb != nil {
        rdb.Close()
    }
}
```

## 修复后的效果

1. **服务启动成功**：即使在没有数据库连接的情况下，服务也能正常启动
2. **接口响应正常**：`/job/analysis` 接口能够快速响应（约 0.068 秒）
3. **错误处理优雅**：数据库和外部服务不可用时，服务会记录警告日志但继续工作
4. **降级功能**：在依赖服务不可用时，使用默认配置继续提供服务

## 测试验证

### 健康检查
```bash
curl -X GET http://localhost:8080/service/liveness
# 响应：{"code":"Success","message":"Service is alive"}
```

### 作业分析接口
```bash
curl -X POST http://localhost:8080/job/analysis -H 'Content-Type: application/json' -d '{
    "engine_model": "mps-transcode-new",
    "engine_params": "{\"format\":\"mp4\",\"video\":{\"codec\":\"h264\",\"bitrate\":\"1000k\"},\"audio\":{\"codec\":\"aac\",\"bitrate\":\"128k\"}}",
    "user_id": "1207704461481212",
    "job_id": "test-job-1752196952",
    "slice_process": false,
    "analysis_mode": "byParseEngineParams",
    "tag": "test",
    "max_slice_num": 10,
    "min_slice_duration": 30,
    "use_worker_brain_result": false,
    "invoke_worker_brain": false,
    "speed_x_range": ["5X"],
    "is_auto_speed_x": false,
    "pipeline_id": "test-pipeline-123",
    "request_id": "test-request-1752196952"
}'
```

响应时间：约 0.068 秒，返回正常的分析结果。

## 最佳实践建议

1. **优雅降级**：在依赖服务不可用时，应该提供降级功能而不是直接失败
2. **超时控制**：为所有外部调用设置合理的超时时间
3. **错误处理**：使用 `log.Error` 而不是 `log.Fatal`，除非确实需要终止程序
4. **资源检查**：在使用资源前始终检查其是否为 nil
5. **监控和日志**：添加详细的日志记录，便于问题排查

## 配置文件

创建了测试配置文件 `config_test.yaml`，可以在本地环境中使用：
```bash
cd apps/analysis
go run cmd/server/main.go cmd/server/wire_gen.go -conf ./configs/config_test.yaml
```
