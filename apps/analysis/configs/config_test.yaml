server:
  http:
    addr: 0.0.0.0:8080
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s

data:
  database:
    driver: mysql
    source: root:@tcp(127.0.0.1:3306)/test?charset=utf8mb4&parseTime=True&loc=Local
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
  redis:
    addr: 127.0.0.1:6379
    password: ""
    db: 0
    dial_timeout: 5s
    read_timeout: 3s
    write_timeout: 3s

external_services:
  dmes:
    url: "http://localhost:8081"
    timeout: 30s
  worker_brain:
    mps_transcode_new_url: "http://localhost:8082"
    mps_editing_url: "http://localhost:8083"
    timeout: 30s
  sla:
    url: "http://localhost:8084"
    timeout: 30s
  media_meta:
    url: "http://localhost:8085"
    timeout: 30s

app:
  region: "cn-shanghai"
  deploy_env: "dev"
  # registry:
  #   endpoint: "http://localhost:8848"
  media:
    default_disk_times_for_multi_transcode: 10
    max_disk_for_multi_transcode: 100000
    min_disk_for_multi_transcode: 10000
    service_bucket_list: "live-aliyun-record-sh,mts-log-param-cn-shanghai"

log:
  level: "debug"
  format: "json"
  output: "stdout"
