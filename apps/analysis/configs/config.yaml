server:
  http:
    addr: 0.0.0.0:8080
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s

data:
  database:
    driver: mysql
    source: mpp_tc:mpp_1201@tcp(rm-uf6j2m2a820vw8b7o.mysql.rds.aliyuncs.com:3306)/mpp_tc_job_mps?charset=utf8mb4&parseTime=True&loc=Local
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
  redis:
    addr: 127.0.0.1:6379
    password: ""
    db: 0
    dial_timeout: 5s
    read_timeout: 3s
    write_timeout: 3s

external_services:
  dmes:
    url: "http://10.86.219.53:80"
    timeout: 30s
  worker_brain:
    mps_transcode_new_url: "http://10.86.219.53:80/sendRequest"
    mps_editing_url: "http://21.43.81.138"
    timeout: 30s
  sla:
    url: "http://10.86.143.167:8080"
    timeout: 30s
  media_meta:
    url: "http://mpp-media-meta:8080/"
    timeout: 30s

app:
  region: "cn-shanghai"
  deploy_env: "dev"
  registry:
    endpoint: "http://mse-35eb3ee0-nacos-ans.mse.aliyuncs.com:8848"
  media:
    default_disk_times_for_multi_transcode: 10
    max_disk_for_multi_transcode: 100000
    min_disk_for_multi_transcode: 10000
    service_bucket_list: "live-aliyun-record-sh,mts-log-param-cn-shanghai"

log:
  level: "info"
  format: "json"
  output: "stdout"
