#!/bin/bash

# API测试脚本
BASE_URL="http://localhost:8080"

echo "=== MPS Job Analysis API 测试 ==="

# 1. 健康检查测试
echo "1. 测试存活性检查..."
curl -X GET "${BASE_URL}/service/liveness" \
  -H "Content-Type: application/json" \
  | jq .

echo -e "\n2. 测试就绪性检查..."
curl -X GET "${BASE_URL}/service/readiness" \
  -H "Content-Type: application/json" \
  | jq .

# 2. 作业分析测试 - byParseEngineParams模式
echo -e "\n3. 测试作业分析 - byParseEngineParams模式..."
curl -X POST "${BASE_URL}/job/analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "engine_model": "mps-transcode-new",
    "engine_params": "{\"format\":\"mp4\",\"video\":{\"codec\":\"h264\",\"bitrate\":\"1000k\"},\"audio\":{\"codec\":\"aac\",\"bitrate\":\"128k\"}}",
    "user_id": "1207704461481212",
    "job_id": "test-job-' $(date +%s) '",
    "slice_process": false,
    "analysis_mode": "byParseEngineParams",
    "tag": "test",
    "max_slice_num": 10,
    "min_slice_duration": 30,
    "use_worker_brain_result": false,
    "invoke_worker_brain": false,
    "speed_x_range": ["5X"],
    "is_auto_speed_x": false,
    "pipeline_id": "test-pipeline-123",
    "request_id": "test-request-' $(date +%s) '"
  }' | jq .

# 3. 作业分析测试 - byScheduleParams模式
echo -e "\n4. 测试作业分析 - byScheduleParams模式..."
curl -X POST "${BASE_URL}/job/analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "engine_model": "mps-transcode-new",
    "engine_params": "{\"format\":\"mp4\"}",
    "user_id": "1207704461481212",
    "job_id": "test-job-schedule-' $(date +%s) '",
    "slice_process": true,
    "analysis_mode": "byScheduleParams",
    "schedule_params": {
      "pipeline_id": "2c416551f7564aa48453a4ad9279f534",
      "quota_set": {
        "cpu": 2000,
        "gpu": 1000,
        "disk": 3000
      },
      "priority": 6,
      "expect_cost_time": 500,
      "inputs": [
        {
          "duration": 120.5,
          "video_bitrate": 2000000,
          "avg_fps": 25.0,
          "size": 104857600,
          "format": "mp4",
          "fps": 25.0,
          "width": 1920,
          "audio_codec": "aac",
          "audio_bitrate": 128000,
          "height": 1080,
          "video_codec": "h264"
        }
      ],
      "configs": [
        {
          "duration": 120.5,
          "format": "mp4",
          "fps": 25.0,
          "id": "config-1",
          "audio_codec": "aac",
          "job_type": "Transcode",
          "template_id": "template-123",
          "audio_bitrate": 128000,
          "video_codec": "h264",
          "audio_only": false,
          "by_worker_brain": false
        }
      ],
      "parallel_num": 4,
      "sla_level": "standard",
      "slice_num": 4,
      "speed_x_range": ["5X", "10X"]
    },
    "tag": "test-schedule",
    "max_slice_num": 10,
    "min_slice_duration": 30,
    "use_worker_brain_result": false,
    "invoke_worker_brain": false,
    "speed_x_range": ["5X", "10X"],
    "is_auto_speed_x": false,
    "pipeline_id": "test-pipeline-456",
    "request_id": "test-request-schedule-' $(date +%s) '"
  }' | jq .

# 4. 结果报告测试
echo -e "\n5. 测试结果报告..."
curl -X POST "${BASE_URL}/job/reportResult" \
  -H "Content-Type: application/json" \
  -d '{
    "job_id": "test-job-report-' $(date +%s) '",
    "alloc_quota_set": {
      "cpu": 1000,
      "gpu": 0,
      "disk": 1000
    },
    "max_quota_set": {
      "cpu": 1200,
      "gpu": 0,
      "disk": 1200
    },
    "avg_quota_set": {
      "cpu": 800,
      "gpu": 0,
      "disk": 800
    },
    "engine_model": "mps-transcode-new",
    "engine_params": "{\"format\":\"mp4\"}",
    "expect_cost_time": 300,
    "real_cost_time": 280,
    "pipeline_id": "test-pipeline-789",
    "tag": "test-report",
    "request_id": "test-request-report-' $(date +%s) '",
    "product": "mps",
    "user_id": "1207704461481212",
    "env": "test",
    "station": "test-station"
  }' | jq .

echo -e "\n=== API测试完成 ==="
