// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.1
// source: agent/config.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgentExternalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// nacos endpoint
	NacosEndpoint string `protobuf:"bytes,1,opt,name=nacosEndpoint,proto3" json:"nacosEndpoint,omitempty"`
	// tracer_endpoint
	TracerEndpoint string `protobuf:"bytes,2,opt,name=tracerEndpoint,proto3" json:"tracerEndpoint,omitempty"`
	// metrics_endpoint
	MetricsEndpoint string `protobuf:"bytes,3,opt,name=metricsEndpoint,proto3" json:"metricsEndpoint,omitempty"`
}

func (x *AgentExternalConfig) Reset() {
	*x = AgentExternalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentExternalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentExternalConfig) ProtoMessage() {}

func (x *AgentExternalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentExternalConfig.ProtoReflect.Descriptor instead.
func (*AgentExternalConfig) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{0}
}

func (x *AgentExternalConfig) GetNacosEndpoint() string {
	if x != nil {
		return x.NacosEndpoint
	}
	return ""
}

func (x *AgentExternalConfig) GetTracerEndpoint() string {
	if x != nil {
		return x.TracerEndpoint
	}
	return ""
}

func (x *AgentExternalConfig) GetMetricsEndpoint() string {
	if x != nil {
		return x.MetricsEndpoint
	}
	return ""
}

// ConfigurationItem represents all the configuration with its name(key).
type AgentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent net ip
	NetIp string `protobuf:"bytes,1,opt,name=netIp,proto3" json:"netIp,omitempty"`
	// external config
	ExternalConfig *AgentExternalConfig `protobuf:"bytes,2,opt,name=externalConfig,proto3" json:"externalConfig,omitempty"`
	// nacos endpoint
	NacosEndpoint string `protobuf:"bytes,3,opt,name=nacosEndpoint,proto3" json:"nacosEndpoint,omitempty"`
	// tracer_endpoint
	TracerEndpoint string `protobuf:"bytes,4,opt,name=tracerEndpoint,proto3" json:"tracerEndpoint,omitempty"`
	// metrics_endpoint
	MetricsEndpoint string `protobuf:"bytes,5,opt,name=metricsEndpoint,proto3" json:"metricsEndpoint,omitempty"`
	// oss endpoint
	OssEndpoint string `protobuf:"bytes,6,opt,name=ossEndpoint,proto3" json:"ossEndpoint,omitempty"`
	// mns endpoint
	MnsEndpoint string `protobuf:"bytes,7,opt,name=mnsEndpoint,proto3" json:"mnsEndpoint,omitempty"`
}

func (x *AgentConfig) Reset() {
	*x = AgentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentConfig) ProtoMessage() {}

func (x *AgentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentConfig.ProtoReflect.Descriptor instead.
func (*AgentConfig) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{1}
}

func (x *AgentConfig) GetNetIp() string {
	if x != nil {
		return x.NetIp
	}
	return ""
}

func (x *AgentConfig) GetExternalConfig() *AgentExternalConfig {
	if x != nil {
		return x.ExternalConfig
	}
	return nil
}

func (x *AgentConfig) GetNacosEndpoint() string {
	if x != nil {
		return x.NacosEndpoint
	}
	return ""
}

func (x *AgentConfig) GetTracerEndpoint() string {
	if x != nil {
		return x.TracerEndpoint
	}
	return ""
}

func (x *AgentConfig) GetMetricsEndpoint() string {
	if x != nil {
		return x.MetricsEndpoint
	}
	return ""
}

func (x *AgentConfig) GetOssEndpoint() string {
	if x != nil {
		return x.OssEndpoint
	}
	return ""
}

func (x *AgentConfig) GetMnsEndpoint() string {
	if x != nil {
		return x.MnsEndpoint
	}
	return ""
}

var File_agent_config_proto protoreflect.FileDescriptor

var file_agent_config_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x22, 0x8d, 0x01,
	0x0a, 0x13, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x61,
	0x63, 0x6f, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xb5, 0x02,
	0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x65, 0x74, 0x49, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x65,
	0x74, 0x49, 0x70, 0x12, 0x54, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x61, 0x63,
	0x6f, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x73, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x73, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6e, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6e, 0x73, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x42, 0x2a, 0x5a, 0x09, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0xf8, 0x01, 0x01, 0xaa, 0x02, 0x19, 0x4d, 0x70, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_config_proto_rawDescOnce sync.Once
	file_agent_config_proto_rawDescData = file_agent_config_proto_rawDesc
)

func file_agent_config_proto_rawDescGZIP() []byte {
	file_agent_config_proto_rawDescOnce.Do(func() {
		file_agent_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_config_proto_rawDescData)
	})
	return file_agent_config_proto_rawDescData
}

var file_agent_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_config_proto_goTypes = []interface{}{
	(*AgentExternalConfig)(nil), // 0: api.pkg.agent.config.v1.AgentExternalConfig
	(*AgentConfig)(nil),         // 1: api.pkg.agent.config.v1.AgentConfig
}
var file_agent_config_proto_depIdxs = []int32{
	0, // 0: api.pkg.agent.config.v1.AgentConfig.externalConfig:type_name -> api.pkg.agent.config.v1.AgentExternalConfig
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_agent_config_proto_init() }
func file_agent_config_proto_init() {
	if File_agent_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_agent_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentExternalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_config_proto_goTypes,
		DependencyIndexes: file_agent_config_proto_depIdxs,
		MessageInfos:      file_agent_config_proto_msgTypes,
	}.Build()
	File_agent_config_proto = out.File
	file_agent_config_proto_rawDesc = nil
	file_agent_config_proto_goTypes = nil
	file_agent_config_proto_depIdxs = nil
}
