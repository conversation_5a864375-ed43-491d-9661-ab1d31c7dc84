// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.29.0
// source: stream/proto/http.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// HttpMessageBody http 消息体
type HTTPMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// http method
	Method string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	// http url
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// http headers
	Header map[string]string `protobuf:"bytes,3,rep,name=header,proto3" json:"header,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// http status
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	// body
	Body []byte `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *HTTPMessage) Reset() {
	*x = HTTPMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_stream_proto_http_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPMessage) ProtoMessage() {}

func (x *HTTPMessage) ProtoReflect() protoreflect.Message {
	mi := &file_stream_proto_http_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPMessage.ProtoReflect.Descriptor instead.
func (*HTTPMessage) Descriptor() ([]byte, []int) {
	return file_stream_proto_http_proto_rawDescGZIP(), []int{0}
}

func (x *HTTPMessage) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *HTTPMessage) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *HTTPMessage) GetHeader() map[string]string {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *HTTPMessage) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *HTTPMessage) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

var File_stream_proto_http_proto protoreflect.FileDescriptor

var file_stream_proto_http_proto_rawDesc = []byte{
	0x0a, 0x17, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68,
	0x74, 0x74, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x6b, 0x67, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x76, 0x31, 0x22, 0xe8, 0x01, 0x0a, 0x0b, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x48, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x1a, 0x39, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x2a, 0x5a,
	0x10, 0x70, 0x6b, 0x67, 0x2f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0xf8, 0x01, 0x01, 0xaa, 0x02, 0x12, 0x4d, 0x70, 0x70, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_stream_proto_http_proto_rawDescOnce sync.Once
	file_stream_proto_http_proto_rawDescData = file_stream_proto_http_proto_rawDesc
)

func file_stream_proto_http_proto_rawDescGZIP() []byte {
	file_stream_proto_http_proto_rawDescOnce.Do(func() {
		file_stream_proto_http_proto_rawDescData = protoimpl.X.CompressGZIP(file_stream_proto_http_proto_rawDescData)
	})
	return file_stream_proto_http_proto_rawDescData
}

var file_stream_proto_http_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_stream_proto_http_proto_goTypes = []interface{}{
	(*HTTPMessage)(nil), // 0: api.pkg.stream.proto.v1.HTTPMessage
	nil,                 // 1: api.pkg.stream.proto.v1.HTTPMessage.HeaderEntry
}
var file_stream_proto_http_proto_depIdxs = []int32{
	1, // 0: api.pkg.stream.proto.v1.HTTPMessage.header:type_name -> api.pkg.stream.proto.v1.HTTPMessage.HeaderEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_stream_proto_http_proto_init() }
func file_stream_proto_http_proto_init() {
	if File_stream_proto_http_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_stream_proto_http_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_stream_proto_http_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_stream_proto_http_proto_goTypes,
		DependencyIndexes: file_stream_proto_http_proto_depIdxs,
		MessageInfos:      file_stream_proto_http_proto_msgTypes,
	}.Build()
	File_stream_proto_http_proto = out.File
	file_stream_proto_http_proto_rawDesc = nil
	file_stream_proto_http_proto_goTypes = nil
	file_stream_proto_http_proto_depIdxs = nil
}
