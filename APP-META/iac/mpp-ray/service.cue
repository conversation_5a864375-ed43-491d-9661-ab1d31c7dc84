// 完整的配置及字段说明请参考 https://yuque.antfin-inc.com/docs/share/583ad8c2-01e9-4d6a-9fec-15a2d1038448?# 《serverless IaC模型解释》
// 命令行工具使用参考 https://yuque.antfin-inc.com/docs/share/f145b1de-5e82-46f8-af56-5dbc4629b68c?# 《gitops 命令行工具》
// CUE 常见问题 https://yuque.antfin-inc.com/docs/share/16718ca5-ed76-4dc9-b4f5-aaf833c000d9?# 《cue一百问》
//
package main

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
	ctr "gitlab.alibaba-inc.com/aone-oss/serverless-iac/container"
	r "gitlab.alibaba-inc.com/aone-oss/serverless-iac/resource" // 定义别名为r, 避免包名与字段名一样造成循环引用
	"gitlab.alibaba-inc.com/aone-oss/serverless-iac/workloadtype"
)

ASI_BY_ECI?: _
MPP_RAY_SLB?: _
MPP_DEPLOY_TYPE?: _

//单独声明ilogtail sidecar配置
#LogtailSidecar: ctr.#Sidecar & {
  name: "logtail"
  if APPSTACK_REGION == "me-east-1" {
    image: "registry-me-central-1.ack.aliyuncs.com/acs/logtail:v1.8.7.0-aliyun"
  }
  if APPSTACK_REGION != "me-east-1" {
    image: "registry-"+APPSTACK_REGION+"-vpc.ack.aliyuncs.com/acs/logtail:v1.8.7.0-aliyun"
  }
  env: [
    {name: "IS_SIDECAR", value: "true"},
    {name: "SIGMA_IGNORE_RESOURCE", value: "true"},
    {name: "ALIYUN_LOGTAIL_CONFIG",value:"/etc/ilogtail/conf/"+APPSTACK_REGION+"-internet/ilogtail_config.json"},
    //仅需要修改下面两个字段配置, 其他全部保持不变COPY至cue即可
    {name: "ALIYUN_LOGTAIL_USER_ID", value: "1426105496228119"},
    {name: "APP_NAME", value: APPSTACK_APP_NAME},
    {name: "APPSTACK_ENV_LEVEL", value: APPSTACK_ENV_LEVEL},
    {name: "APPSTACK_AZ", value: APPSTACK_AZ},
    {name: "APPSTACK_ENV_TAG", value: APPSTACK_ENV_TAG},
    {name: "APPSTACK_REGION", value: APPSTACK_REGION},
    {name: "APPSTACK_STAGE", value: APPSTACK_STAGE},
    //日志机器组标签，APP_NAME区分应用 ENV_LEVEL区分多套环境
    {name: "ALIYUN_LOGTAIL_USER_DEFINED_ID", value: APPSTACK_APP_NAME + "-" + APPSTACK_ENV_LEVEL},
	  {name: "ALIYUN_LOG_ENV_TAGS", value: "CSE_INSTANCE_ID|CSE_POD_IP|CSE_POD_NAME|CSE_POD_NAMESPACE|CSE_NODE_IP|CSE_NODE_NAME|APP_NAME|APPSTACK_ENV_TAG"},
    ]

  // 存活探针
	livenessProbe: {
		 initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
		 periodSeconds:       30 // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
		 timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
		 successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
		 failureThreshold:    3  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
		 exec: [ "/etc/init.d/ilogtaild", "status"]
	 }
  // 资源
  resource: r.#Schema & {
    cpu:    "1"
    memory: "1G"
  }
}

//serverless.#Service你可以理解为java里面的一个class定义，myApp是这个class的具体object
myApp: serverless.#Service & {
	timezone: "Asia/Shanghai" // 时区
	sidecar: [#LogtailSidecar]
	// 优雅退出等待时间 https://aliyuque.antfin.com/cnp/gitops/gp6v01?#qKwyJ
  terminationGracePeriodSeconds: 300

	// 主进程容器
	mainContainer: {
		cmd: ["/home/<USER>/start.sh"] // 主进程启动命令
		args: ["standalone"] // 主进程启动参数

		env: [
      {name: "APPSTACK_ENV_LEVEL", value: APPSTACK_ENV_LEVEL},
      {name: "APPSTACK_AZ", value: APPSTACK_AZ},
      {name: "APPSTACK_ENV_TAG", value: APPSTACK_ENV_TAG},
      {name: "APPSTACK_REGION", value: APPSTACK_REGION},
      {name: "APPSTACK_STAGE", value: APPSTACK_STAGE},

      {name: "CLOUD_REGION", value: APPSTACK_REGION},
      {name: "APP_REDIS_ADDRESS", value: APP_REDIS_ADDRESS},
      {name: "APP_REDIS_PASSWORD", value: APP_REDIS_PASSWORD},
//      {name: "APP_TRACER_ENDPOINT", value: APP_TRACER_ENDPOINT},
		]

		// 配置存活、就绪和启动探测器，请根据实际情况修改下面的配置，文档参考
		// https://kubernetes.io/zh/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
		// 启动探针, 启动探针探测成功之后存活探测才开始工作
		startupProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       10 // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    300000  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
      exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}

		// 存活探针
		livenessProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       5  // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    3  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
			exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}

		// 就绪探针
		readinessProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       10 // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    3  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
      exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}
	}


    // SLB
    if MPP_RAY_SLB != _|_ {
       slbs: [{
           slbInstanceId: MPP_RAY_SLB
           // forceOverrideListeners: "true"
           annotations: {
               "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-type": "tcp" // 将SLB的健康检查类型设置为http类型
//               "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-method": "get"
//               "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-uri": "/api/version"
           }
           ports: [
               {
                   name:       "mpp-ray-dashboard"
                   protocol:   "TCP"
                   port:       8265
                   targetPort: 8265
               },
               {
                   name:       "mpp-ray-gcs"
                   protocol:   "TCP"
                   port:       6379
                   targetPort: 6379
               },
           ]
       }]
    }

	// 实例数和实例规格
	// 不同环境等级使用不用配置
	// APPSTACK_ENV_LEVEL 为环境内置标签名, 参考 https://yuque.antfin-inc.com/docs/share/9a1d662d-0498-48da-8da1-548034d69246#558a5489
	// 规格请参考: https://yuque.antfin-inc.com/cnp/gitops/gp6v01#aj8Zt
	if APPSTACK_ENV_LEVEL == "staging-ncloud" {
		replica:  1
		resource: {
			cpu:    "8"
			memory: "16Gi"
		}
	}

	if APPSTACK_ENV_LEVEL == "staging1-ncloud" {
		replica:  2
   	resource: {
			cpu:    "8"
			memory: "16Gi"
		}
  }

	if APPSTACK_ENV_LEVEL == "staging2-ncloud" {
		replica:  2
   	resource: {
			cpu:    "8"
			memory: "16Gi"
		}
	}

	if APPSTACK_ENV_LEVEL == "production-ncloud" {
		replica:  2
   	resource: {
			cpu:    "8"
			memory: "16Gi"
		}
	}

	// 其他环境等级用以下配置
  // 注意不要漏写 if 对应的 else 部分，否则推导时没匹配上的环境会报配置缺失错误，另外 cue  不支持 else
	if APPSTACK_ENV_LEVEL != "production-ncloud" &&  APPSTACK_ENV_LEVEL != "staging-ncloud" &&  APPSTACK_ENV_LEVEL!= "staging1-ncloud" &&  APPSTACK_ENV_LEVEL!= "staging2-ncloud" {
		replica:  2
   	resource: {
			cpu:    "8"
			memory: "16Gi"
		}
	}

  if ASI_BY_ECI != _|_ {
        //污点配置
				//exclusiveResource: { multipleExclusiveResource: "mpp.aliyun.com/product=mps,sigma.ali/resource-pool=mcp,virtual-kubelet.io/provider=alibabacloud" }
        exclusiveResource: { multipleExclusiveResource: "virtual-kubelet.io/provider=alibabacloud" }
        //acrInstanceId: string
        podAnnotations: {
        	  "k8s.aliyun.com/eci-instance-type": "ecs.c8a.16xlarge,ecs.c8i.16xlarge,ecs.c7a.16xlarge,ecs.c6a.16xlarge"
            "k8s.aliyun.com/eci-custom-tags": "product:mps,biz:mpp,engine:" + APPSTACK_APP_NAME
            "alibabacloud.com/user-elastic-cluster": "true"
             // Set memory request （只有加了这句话才能让 Memory request 生效）
            "k8s.aliyun.com/eci-container-resource-view": "true"
            "pods.sigma.alibaba-inc.com/inject-name-as-sn": "true"

            // 注入 pod annotation  https://yuque.antfin-inc.com/docs/share/583ad8c2-01e9-4d6a-9fec-15a2d1038448?#Av2nC
            //	"k8s.aliyun.com/eci-use-specs": "ecs.g7se.6xlarge,ecs.g7.6xlarge,ecs.g6.6xlarge,ecs.c5.xlarge,24-96Gi"
        }
   }

		if MPP_DEPLOY_TYPE != _|_ {
			exclusiveResource: {
				multipleExclusiveResource:"mpp.aliyun.com/product=mpp,mcp.aliyun.com/resource-pool=mcp,sigma.ali/resource-pool=mcp"
			}
		}

    if ASI_BY_ECI == _|_ {
    	// rms
    	podAnnotations: {
    			// 视频云-上海-生产
					if (APPSTACK_AZ == "asi-video-pre" || APPSTACK_AZ == "asi-video-prd") && APPSTACK_REGION == "cn-shanghai" {
						"sigma.ali/user-id":               "1426105496228119"
						"sigma.ali/user-securitygroup-id": "sg-uf6i8760qmne3yg8o6x5"
						"sigma.ali/user-vswitch-list":     "vsw-uf6dhp0jrm1vo4jx4bocx,vsw-uf6drqr9z5nw2v2ndsb7i,vsw-uf6p72f76671ngyi2bno6"
						"alibabacloud.com/zone-id":        "cn-shanghai-m,cn-shanghai-g,cn-shanghai-b"
						"sigma.ali/user-vpc-id":           "vpc-uf64sbnoec0t90bys5ukw"
					}
    	}
    }


//    重建升级：按需取消注释，谨慎使用，直播某些业务不支持重建升级。
//    workloadType: workloadtype.#CloneSet
//		workloadSettings: {
//				// 原地升级（默认 InPlaceIfPossible）
//				// https://aliyuque.antfin.com/cnp/gitops/gp6v01?#ksVPr
//				// https://openkruise.io/zh/docs/user-manuals/cloneset#%E5%8D%87%E7%BA%A7%E5%8A%9F%E8%83%BD
//        spec: json.Marshal({updateStrategy: "ReCreate", maxSurge: "100%", maxUnavailable: "50%"})
//				//spec: json.Marshal({cloneSetMode: "community"})
//		}

	if APPSTACK_ENV_LEVEL == "staging-ncloud" {
			// 发布策略
//			releaseStrategy: batches: 1
			// 发布策略
			releaseStrategy: {
				betaReplicas: 1            // beta实例数. 优先发布beta实例, 然后再按照stepWeight实例数继续发布
				stepWeight:   50           // 流量百分比, 每批最小实例是1个。例如设置20，会按5批发，但是总共只有3个实例，最终会按三批发(每批最小实例是1个)
				pausePolicy:  "firstPause" // 暂停策略，manual：每批暂停 | firstPause：只首批暂停 | auto：每批都不暂停
				interval:     "10s"        // auto时每批发布间隔时间. 30s, 10m, 1h等.  默认20s.
			}
			workloadType: workloadtype.#CloneSet
			workloadSettings: {
				spec: json.Marshal({
					// CloneSet类型支持 ReCreate（强制重建发布）、InPlaceIfPossible（尽量原地升级发布）、InPlaceOnly（只原地升级）
					updateStrategy: "InPlaceIfPossible"
					maxSurge: "100%"
					maxUnavailable: "50%"
					cloneSetMode: "community"
				})
			}
	}

	if APPSTACK_ENV_LEVEL != "staging-ncloud" {
			// 发布策略
			releaseStrategy: {
				betaReplicas: 1            // beta实例数. 优先发布beta实例, 然后再按照stepWeight实例数继续发布
				stepWeight:   50           // 流量百分比, 每批最小实例是1个。例如设置20，会按5批发，但是总共只有3个实例，最终会按三批发(每批最小实例是1个)
				pausePolicy:  "firstPause" // 暂停策略，manual：每批暂停 | firstPause：只首批暂停 | auto：每批都不暂停
				interval:     "10s"        // auto时每批发布间隔时间. 30s, 10m, 1h等.  默认20s.
			}
			workloadSettings: {
				// 原地升级（默认 InPlaceIfPossible）
				// https://aliyuque.antfin.com/cnp/gitops/gp6v01?#ksVPr
			  // https://openkruise.io/zh/docs/user-manuals/cloneset#%E5%8D%87%E7%BA%A7%E5%8A%9F%E8%83%BD
        // spec: json.Marshal({updateStrategy: "ReCreate", maxSurge: "100%", maxUnavailable: "50%"})
		    spec: json.Marshal({
					updateStrategy: "ReCreate"
					maxSurge: "100%"
					maxUnavailable: 0
				})
			}
	}


	// 应用部署拓扑, 至少2个可用区  https://aliyuque.antfin.com/cnp/gitops/gp6v01#cUcQr
	// 排除迪拜环境
	if APPSTACK_ENV_TAG != "live-record-db-prd" {
	    appZoneTopology: {
            instanceSpreadType: "default"
            whenUnsatisfiable:  "doNotSchedule"
            maxSkew:            1
            minTopologyValues:  2
        }
	}

	volumesConflict: true // 挂载路径冲突时以volumes为准, 默认 false
	// volume 挂载
	// 可选，请根据实际情况配置
	volumes: [
		{
			mounts: [
				{
					container: "*" // 所有容器
					path:      "/home/<USER>/logs"
					readOnly:  false
				},
			]
			emptyDir: {
			}
		},
		{
				mounts: [
						{
								container: "*" // 所有容器
								path:      "/home/<USER>/disk"
								readOnly:  false
						},
				]
				emptyDir: {
				}
		},
	]
}
