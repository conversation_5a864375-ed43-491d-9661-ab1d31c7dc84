# 基础镜像
FROM hub.docker.alibaba-inc.com/aone-base-global/mpp-baseline:20240802160506

ARG APP_NAME=mpp-ray
ENV APP_NAME=${APP_NAME}

# 不需要java环境
ENV RUN_JAVA_ENABLED=false

RUN yum install gcc gcc-c++ -y && \
    curl -L -O "https://mirror.nju.edu.cn/github-release/conda-forge/miniforge/LatestRelease/Miniforge3-$(uname)-$(uname -m).sh" && \
    bash Miniforge3-$(uname)-$(uname -m).sh -b -p /opt/conda && rm -f Miniforge3-$(uname)-$(uname -m).sh && \
    source /opt/conda/bin/activate && \
    mamba install python=3.12 -y && \
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple && \
    pip install ray[all] && \
    rm -rf /opt/conda/pkgs/* && \
    echo "source /opt/conda/bin/activate" >> /etc/profile && \
    echo "install ok"

COPY app /home/<USER>/$APP_NAME
#COPY start_in_background.sh /home/<USER>/start_in_background.sh
# 将构建出的主包复制到指定镜像目录中
ADD $APP_NAME.tgz /home/<USER>/$APP_NAME/
